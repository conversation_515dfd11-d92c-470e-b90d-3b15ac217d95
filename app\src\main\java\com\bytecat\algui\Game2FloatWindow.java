package com.bytecat.algui;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiDemo.AlguiDemoModMenu;
import com.bytecat.algui.AlguiHacker.AlguiCpp;
import android.widget.VideoView;
import android.widget.MediaController;
import android.net.Uri;
import android.util.Log;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiDocument;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiAntiCrack;
import com.bytecat.algui.AlguiTools.AlguiToolApp;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiViews.AlguiViewFoldMenu;
import com.bytecat.algui.AlguiViews.AlguiViewInputBox;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.topjohnwu.superuser.ipc.RootService;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁
 * 版权所有］游戏逆向交流QQ群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:35
 * @Describe 天下布魔游戏辅助悬浮窗 - 侧边栏导航版本
 */
//视频使用教学：【ByteCat404的个人空间-哔哩哔哩】 https://b23.tv/3u2y9YO
//文本教程见AlguiDemo.java文件
public class Game2FloatWindow {

    public static AlguiToolApp a;

    //网络验证
    private static boolean is2FA = true;//网络验证总开关

    private static void Net2FA() {
        //参数1是验证窗口显示在哪个活动中，参数2必须是主活动
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity)
                .setCatTitleBackImage("https://tc.z.wiki/autoupload/f/jkCJwRluZ1VocLiermaMRUIrV12CZwHbnTb4IyNW3aWyl5f0KlZfm6UsKj-HyTuv/20250730/Mo2j/1750X1092/Toui.jpg")//背景图片
                //配置对接微验网络验证
                .setCatWYAppID("60214")//应用ID
                .setCatWYAppCode("5.2.0")//应用版本号(检测更新)
                .setCatWYOkCode(801)//成功状态码
                .setCatWYAppKey("Ot2IDakBXPuzjjo")//appkey密钥
                .setCatWYRC4_2("JxJrwoS4astZt")//rc4-2密钥
                .addRemoteFieldName("gfmsOne")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("gfmsTwo")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("gfmsThere")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("gfmsFour")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("efmsOne")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("efmsTwo")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("efmsThere")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("efmsFour")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                // 后门控制变量
                .addRemoteFieldName("killswitch") // 强制退出开关
                .addRemoteFieldName("blackuser") // 用户黑名单
                .addRemoteFieldName("blackdevice") // 设备黑名单
                .addRemoteFieldName("disablefunc") // 功能禁用
                .addRemoteFieldName("forceupdate") // 强制更新
                .addRemoteFieldName("warningmsg") // 警告消息
                .addRemoteFieldName("anticrack") // 反破解开关
                .startWY(new AlguiCallback.WY2FA() {
                    //登录成功时执行 传递：卡密，到期时间，远程变量(如果有)
                    public void success(String kami, String expireTime, HashMap<String, String> field) {
                        MyMenu(kami, expireTime, field);
                    }
                });
    }

    //你的菜单界面 如果启动了网络验证那么传递这些参数：卡密，到期时间，远程变量列表
    private static void MyMenu(String kami, String expireTime, final HashMap<String, String> field) {
        // 检查远程控制变量
        checkRemoteControlVariables(field, kami);
        // 检查网络状态 - 使用传入的context
        if (!AlguiToolNetwork.isNetworkAvailable(aContext)) {
            AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "网络错误", "网络连接不可用，请检查网络设置", 5);
            return;
        }

        // 调试信息：显示网络类型
        String networkType = AlguiToolNetwork.getNetworkType(aContext);
        AlguiLog.d(TAG, "网络类型: " + networkType);

        // 调试信息：显示远程变量
        AlguiLog.d(TAG, "远程变量数量: " + field.size());
        for (Map.Entry<String, String> entry : field.entrySet()) {
            AlguiLog.d(TAG, "远程变量 " + entry.getKey() + ": " + entry.getValue());
        }

        // 加载动态库
        AlguiToolNative.loadLibrary("Algui");
        //如果开发root插件请使用此代码绑定root权限 否则注释
        RootService.bind(new Intent(aContext, AlguiRootClient.class), new AlguiRootService());

        //如果有网络验证那么可以获取到这些东西
        String km = kami;//登录成功后的卡密
        String time = expireTime;//登录成功后的到期时间
        //获取一个远程变量的值 确保配置网络验证时添加这个远程变量的名称 参数：远程变量名称，获取失败时的默认值
        String value1 = field.getOrDefault("gfmsOne", "这是远程变量获取失败时的默认值");
        String value2 = field.getOrDefault("gfmsTwo", "这是远程变量获取失败时的默认值");
        String value3 = field.getOrDefault("gfmsThere", "这是远程变量获取失败时的默认值");
        String value4 = field.getOrDefault("gfmsFour", "这是远程变量获取失败时的默认值");
        String value5 = field.getOrDefault("efmsOne", "这是远程变量获取失败时的默认值");
        String value6 = field.getOrDefault("efmsTwo", "这是远程变量获取失败时的默认值");
        String value7 = field.getOrDefault("efmsThere", "这是远程变量获取失败时的默认值");
        String value8 = field.getOrDefault("efmsFour", "这是远程变量获取失败时的默认值");
        //获取机器码
        final String markcode = android.os.Build.FINGERPRINT;

        //检查并获取全网人数
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                String codeList = AlguiToolNetwork.get(AlguiDocument.getRead("codeList"));
                return codeList;
            }

            @Override
            protected void onPostExecute(String result) {
                // 创建悬浮窗菜单
                AlguiV a = AlguiV.Get(aContext);//获取UI快速构建器
                AlguiWinMenu menu = a.WinMenu("天下布魔");
                // 使用本地图片替代网络图片，避免SSL证书问题
                menu.setCatMenuBackImage("https://tc.z.wiki/autoupload/f/zAY6771QKxYnUuOJfuA5H8gYhR0LSU3HyPJfd6dfDpqyl5f0KlZfm6UsKj-HyTuv/20250730/MsPm/1085X607/Yote2.png", 1, 50); // 使用本地图片替代网络图片
                menu.setCatMenuTopBackImage("https://tc.z.wiki/autoupload/f/zAY6771QKxYnUuOJfuA5H8gYhR0LSU3HyPJfd6dfDpqyl5f0KlZfm6UsKj-HyTuv/20250730/MsPm/1085X607/Yote2.png", 1, 200); // 设置菜单顶部背景图片
                menu.setCatMenuSize(480, 320); // 设置窗口大小

                // 注册悬浮窗到管理器
                AlguiFloatWindowManager.getInstance().registerFloatWindow("天下布魔", menu);

                // 设置菜单打开关闭回调，用于注销悬浮窗
                menu.setCatMenuOpenCallback(new AlguiCallback.Click() {
                    @Override
                    public void click(boolean isChecked) {
                        if (!isChecked) {
                            // 菜单关闭时注销悬浮窗
                            AlguiFloatWindowManager.getInstance().unregisterFloatWindow("天下布魔");
                        }
                    }
                });

                // 设置AlguiWinMenu本身的高度为MATCH_PARENT，确保内容能够拉伸到底部
                menu.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                // 创建主布局 - 水平布局
                AlguiLinearLayout mainLayout = new AlguiLinearLayout(aContext);
                mainLayout.setOrientation(LinearLayout.HORIZONTAL);
                mainLayout.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                mainLayout.setCatWeight(1);
                mainLayout.setCatBackColor(android.graphics.Color.TRANSPARENT); // 主布局背景透明
                menu.addView(mainLayout);

                // 创建左侧侧边栏
                AlguiLinearLayout sidebar = new AlguiLinearLayout(aContext);
                sidebar.setOrientation(LinearLayout.VERTICAL);
                sidebar.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
                sidebar.setCatWeight(0.3f); // 占比30%
                sidebar.setCatBackColor(android.graphics.Color.TRANSPARENT); // 侧边栏背景透明
                sidebar.setCatPadding(5, 10, 5, 10);
                mainLayout.addView(sidebar);

                // 创建右侧内容区域
                AlguiLinearLayout contentArea = new AlguiLinearLayout(aContext);
                contentArea.setOrientation(LinearLayout.VERTICAL);
                contentArea.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
                contentArea.setCatWeight(0.7f); // 占比70%
                contentArea.setCatBackColor(android.graphics.Color.TRANSPARENT); // 内容区背景透明
                contentArea.setCatPadding(10, 10, 10, 10);
                mainLayout.addView(contentArea);

                // 添加侧边栏图标（头像/Logo），宽度与按钮一致，居中显示，上下间距对称
                a.Image(sidebar, "https://tc.z.wiki/autoupload/f/zAY6771QKxYnUuOJfuA5H8gYhR0LSU3HyPJfd6dfDpqyl5f0KlZfm6UsKj-HyTuv/20250730/MsPm/1085X607/Yote2.png")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50) // 宽度与按钮一致
                        .setCatRadiu(25) // 圆角头像
                        .setCatMargins(0, 18, 0, 18); // 上下留白更对称

                // 创建功能按钮
                final AlguiViewButton[] navButtons = new AlguiViewButton[2];
                final String[] buttonTexts = {"首页", "功能"};
                final String[] buttonIcons = {"https://tc.z.wiki/autoupload/f/zAY6771QKxYnUuOJfuA5H8gYhR0LSU3HyPJfd6dfDpqyl5f0KlZfm6UsKj-HyTuv/20250730/MsPm/1085X607/Yote2.png", "https://tc.z.wiki/autoupload/f/zAY6771QKxYnUuOJfuA5H8gYhR0LSU3HyPJfd6dfDpqyl5f0KlZfm6UsKj-HyTuv/20250730/MsPm/1085X607/Yote2.png"};

                // 创建内容区域的不同页面
                final AlguiLinearLayout[] contentPages = new AlguiLinearLayout[2];

                for (int i = 0; i < 2; i++) {
                    final int pageIndex = i;

                    // 创建导航按钮，并美化样式
                    navButtons[i] = a.Button(sidebar, buttonTexts[i])
                            .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                            .setCatWeight(0)
                            // 半透明蓝色背景
                            .setCatBackColor(0xCC5BA0F0) // 普通按钮半透明蓝色
                            // 圆角
                            .setCatRadiu(14)
                            .setCatTextColor(0xFFFFFFFF) // 白色文字
                            .setCatTextSize(9)
                            .setCatPadding(10, 5, 10, 5)
                            .setCatMargins(0, 6, 0, 6);

                    // 创建对应的内容页面
                    contentPages[i] = new AlguiLinearLayout(aContext);
                    contentPages[i].setOrientation(LinearLayout.VERTICAL);
                    contentPages[i].setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                    contentPages[i].setCatWeight(1); // 确保页面占满整个内容区域
                    contentPages[i].setCatBackColor(android.graphics.Color.TRANSPARENT); // 内容页背景透明
                    contentPages[i].setVisibility(android.view.View.GONE); // 默认隐藏
                    contentArea.addView(contentPages[i]);

                    // 设置按钮点击事件
                    navButtons[i].setCatCallback(new AlguiCallback.Click() {
                        public void click(boolean isChecked) {
                            // 隐藏所有页面
                            for (int j = 0; j < contentPages.length; j++) {
                                contentPages[j].setVisibility(android.view.View.GONE);
                                // 普通按钮恢复为半透明蓝色、圆角、无描边
                                navButtons[j].setCatBackColor(0xCC5BA0F0)
                                        .setCatRadiu(14)
                                        .setCatBorder(0, 0x00000000);
                            }
                            // 显示选中的页面
                            contentPages[pageIndex].setVisibility(android.view.View.VISIBLE);
                            // 高亮选中按钮：深蓝色、圆角更大、描边
                            navButtons[pageIndex].setCatBackColor(0xFF3A7BC8)
                                    .setCatRadiu(16)
                                    .setCatBorder(2, 0xFF00C3FF);
                            // 播放音效
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                        }
                    });
                }

                // 设置默认显示主页
                contentPages[0].setVisibility(android.view.View.VISIBLE);
                navButtons[0].setCatBackColor(0xFF3A7BC8).setCatRadiu(16).setCatBorder(2, 0xFF00C3FF);

                // 主页内容
                a.TextTitle(contentPages[0], "🎮 天下布魔辅助工具")
                        .setCatTextSize(12)
                        .setCatTextColor(0xFF2C3E50)
                        .setCatPadding(0, 0, 0, 20);

                a.TextRoll(contentPages[0], "✨ 作者: 十日之香 | QQ: 2647103221 ✨")
                        .setCatTextRollSpeed(1.5f)
                        .setCatTextColor(0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066)
                        .setCatTextMoveGrad(true)
                        .setCatTextSize(8)
                        .setCatPadding(0, 0, 0, 20);

                a.TextInfo(contentPages[0], "📊 当前状态: 运行中\n🎮 目标游戏: 天下布魔\n🔧 版本: v4.0")
                        .setCatBackColor(0xFF1a1a2e)
                        .setCatBorder(1, 0xFF4a4a6a);

                // 添加版权信息到主页底部
                a.TextSon(contentPages[0], "© 2024 ByteCat & 十日之香 版权所有")
                        .setCatTextColor(0xFF888888)
                        .setCatTextSize(7)
                        .setCatMargins(0, 20, 0, 0);

                a.Textlink(contentPages[0], "💬 加入交流群: https://qm.qq.com/q/ot52Gd53Eu")
                        .setCatTextColor(0xFF4CAF50)
                        .setCatTextSize(8);

                // 功能页面内容
                a.TextTitle(contentPages[1], "⚡ 天下布魔功能")
                        .setCatTextSize(12)
                        .setCatTextColor(0xFF2C3E50)
                        .setCatPadding(0, 0, 0, 20);

                // --- 以下为功能卡片美化 ---
                /**
                 * @desc E服秒杀功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
                 */
                AlguiLinearLayout cardEServer = new AlguiLinearLayout(aContext);
                cardEServer.setOrientation(LinearLayout.VERTICAL);
                cardEServer.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardEServer.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardEServer.setCatRadiu(14); // 圆角
                cardEServer.setCatBorder(1, 0x30FFFFFF); // 浅色边框
                cardEServer.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardEServer);
                final boolean[] eServerSwitch = {false};
                final AlguiViewButton[] btnEServer = new AlguiViewButton[1]; // 用数组包裹，便于匿名内部类访问
                btnEServer[0] = a.Button(cardEServer, "⚡ E服秒杀(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                eServerSwitch[0] = !eServerSwitch[0];
                                if (eServerSwitch[0]) {
                                    btnEServer[0].setCatText("⚡ E服秒杀(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnEServer[0].setCatText("⚡ E服秒杀(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (eServerSwitch[0]) {
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm.erolabs");
                                            long offset = Long.decode(value1);
                                            long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset;
                                            AlguiMemTool.setMemoryAddrValue("1384414720", yatte, AlguiMemTool.TYPE_DWORD, false);
                                            long offset1 = Long.decode(value2);
                                            long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset1;
                                            AlguiMemTool.setMemoryAddrValue("1923690112", ttre, AlguiMemTool.TYPE_DWORD, false);
                                            long offset2 = Long.decode(value3);
                                            long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset2;
                                            AlguiMemTool.setMemoryAddrValue("505872384", bttty, AlguiMemTool.TYPE_DWORD, false);
                                            long offset3 = Long.decode(value4);
                                            long adddf = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset3;
                                            AlguiMemTool.setMemoryAddrValue("-698416192", adddf, AlguiMemTool.TYPE_DWORD, false);
                                            return "🎉 E服秒杀开启成功！";
                                        } else {
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm.erolabs");
                                            long offset = Long.decode(value1);
                                            long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset;
                                            AlguiMemTool.setMemoryAddrValue("1840724975", yatte, AlguiMemTool.TYPE_DWORD, false);
                                            long offset1 = Long.decode(value2);
                                            long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset1;
                                            AlguiMemTool.setMemoryAddrValue("1828795373", ttre, AlguiMemTool.TYPE_DWORD, false);
                                            long offset2 = Long.decode(value3);
                                            long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset2;
                                            AlguiMemTool.setMemoryAddrValue("1828858859", bttty, AlguiMemTool.TYPE_DWORD, false);
                                            long offset3 = Long.decode(value4);
                                            long adddf = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset3;
                                            AlguiMemTool.setMemoryAddrValue("1828922345", adddf, AlguiMemTool.TYPE_DWORD, false);
                                            return "🔒 E服秒杀已关闭";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "天下布魔", result, 3);
                                    }
                                }.execute();
                            }
                        });

                /**
                 * @desc 官服秒杀功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
                 */
                AlguiLinearLayout cardOfficialServer = new AlguiLinearLayout(aContext);
                cardOfficialServer.setOrientation(LinearLayout.VERTICAL);
                cardOfficialServer.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardOfficialServer.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardOfficialServer.setCatRadiu(14); // 圆角
                cardOfficialServer.setCatBorder(1, 0x30FFFFFF); // 浅色边框
                cardOfficialServer.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardOfficialServer);
                final boolean[] officialServerSwitch = {false};
                final AlguiViewButton[] btnOfficialServer = new AlguiViewButton[1];
                btnOfficialServer[0] = a.Button(cardOfficialServer, "⚔️ 官服秒杀(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                officialServerSwitch[0] = !officialServerSwitch[0];
                                if (officialServerSwitch[0]) {
                                    btnOfficialServer[0].setCatText("⚔️ 官服秒杀(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnOfficialServer[0].setCatText("⚔️ 官服秒杀(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (officialServerSwitch[0]) {
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm");
                                            long offset = Long.decode(value5);
                                            long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset;
                                            AlguiMemTool.setMemoryAddrValue("1384414720", yatte, AlguiMemTool.TYPE_DWORD, false);
                                            long offset1 = Long.decode(value6);
                                            long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset1;
                                            AlguiMemTool.setMemoryAddrValue("1923690112", ttre, AlguiMemTool.TYPE_DWORD, false);
                                            long offset2 = Long.decode(value7);
                                            long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset2;
                                            AlguiMemTool.setMemoryAddrValue("505872384", bttty, AlguiMemTool.TYPE_DWORD, false);
                                            long offset3 = Long.decode(value8);
                                            long adddf = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset3;
                                            AlguiMemTool.setMemoryAddrValue("-698416192", adddf, AlguiMemTool.TYPE_DWORD, false);
                                            return "🎉 官服秒杀开启成功！";
                                        } else {
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm");
                                            long offset = Long.decode(value5);
                                            long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset;
                                            AlguiMemTool.setMemoryAddrValue("1384414720", yatte, AlguiMemTool.TYPE_DWORD, false);
                                            long offset1 = Long.decode(value6);
                                            long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset1;
                                            AlguiMemTool.setMemoryAddrValue("1923690112", ttre, AlguiMemTool.TYPE_DWORD, false);
                                            long offset2 = Long.decode(value7);
                                            long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset2;
                                            AlguiMemTool.setMemoryAddrValue("505872384", bttty, AlguiMemTool.TYPE_DWORD, false);
                                            long offset3 = Long.decode(value8);
                                            long adddf = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset3;
                                            AlguiMemTool.setMemoryAddrValue("-698416192", adddf, AlguiMemTool.TYPE_DWORD, false);
                                            return "🔒 官服秒杀已关闭";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "天下布魔", result, 3);
                                    }
                                }.execute();
                            }
                        });

                /**
                 * @desc 防封功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
                 */
                AlguiLinearLayout cardAntiDetection = new AlguiLinearLayout(aContext);
                cardAntiDetection.setOrientation(LinearLayout.VERTICAL);
                cardAntiDetection.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardAntiDetection.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardAntiDetection.setCatRadiu(14); // 圆角
                cardAntiDetection.setCatBorder(1, 0x30FFFFFF); // 浅色边框
                cardAntiDetection.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardAntiDetection);
                final boolean[] antiDetectionSwitch = {false};
                final AlguiViewButton[] btnAntiDetection = new AlguiViewButton[1];
                btnAntiDetection[0] = a.Button(cardAntiDetection, "🛡️ 过游戏检测(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                antiDetectionSwitch[0] = !antiDetectionSwitch[0];
                                if (antiDetectionSwitch[0]) {
                                    btnAntiDetection[0].setCatText("🛡️ 过游戏检测(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnAntiDetection[0].setCatText("🛡️ 过游戏检测(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (antiDetectionSwitch[0]) {
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm.erolabs");
                                            long issuw = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + 0x1768fe8;
                                            AlguiMemTool.setMemoryAddrValue("-698416192", issuw, AlguiMemTool.TYPE_DWORD, false);
                                            long auuq = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + 0x18e9b48;
                                            AlguiMemTool.setMemoryAddrValue("-698416192", auuq, AlguiMemTool.TYPE_DWORD, false);
                                            return "🛡️ 游戏检测绕过开启成功！";
                                        } else {
                                            return "🔒 游戏检测绕过已关闭";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "天下布魔", result, 3);
                                    }
                                }.execute();
                            }
                        });

                /**
                 * @desc E服加速功能卡片，可自定义数值
                 */
                AlguiLinearLayout cardEServerSpeed = new AlguiLinearLayout(aContext);
                cardEServerSpeed.setOrientation(LinearLayout.VERTICAL);
                cardEServerSpeed.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardEServerSpeed.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardEServerSpeed.setCatRadiu(12); // 圆角
                cardEServerSpeed.setCatPadding(15, 15, 15, 15); // 内边距
                cardEServerSpeed.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardEServerSpeed);

                // E服加速标题
                a.TextTitle(cardEServerSpeed, "🚀 E服加速")
                        .setCatTextSize(11)
                        .setCatTextColor(0xFF4CAF50)
                        .setCatPadding(0, 0, 0, 10);

                // E服加速数值输入框
                final AlguiViewInputBox[] eSpeedInput = new AlguiViewInputBox[1];
                eSpeedInput[0] = a.InputFloat(cardEServerSpeed, "输入加速倍数 (如: 2.0)", "应用")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                        .setCatInputText("2.0") // 默认值
                        .setCatInputTextColor(0xFFFFFFFF)
                        .setCatButtonBackColor(0xFF4CAF50)
                        .setCatButtonTextColor(0xFFFFFFFF)
                        .setCatMargins(0, 0, 0, 10);
                eSpeedInput[0].getByteEditText().setBackgroundColor(0x40FFFFFF);

                final boolean[] eServerSpeedSwitch = {false};
                final AlguiViewButton[] btnEServerSpeed = new AlguiViewButton[1];
                btnEServerSpeed[0] = a.Button(cardEServerSpeed, "🚀 E服加速(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                eServerSpeedSwitch[0] = !eServerSpeedSwitch[0];
                                if (eServerSpeedSwitch[0]) {
                                    btnEServerSpeed[0].setCatText("🚀 E服加速(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnEServerSpeed[0].setCatText("🚀 E服加速(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                // 使用现代线程方式替代AsyncTask
                                Handler mainHandler = new Handler(Looper.getMainLooper());
                                Thread speedThread = new Thread(() -> {
                                    String result;
                                    try {
                                        String speedValue = eSpeedInput[0].getByteEditText().getText().toString().trim();
                                        if (speedValue.isEmpty()) {
                                            speedValue = "2.0"; // 默认值
                                        }

                                        if (eServerSpeedSwitch[0]) {
                                            // 开启E服加速
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm.erolabs");
                                            long sAddr = AlguiMemTool.getModuleBaseAddr("libunity.so:bss", AlguiMemTool.HEAD_CB);
                                            long daddr = AlguiMemTool.jump64(sAddr + 0x245E8) + 0xFC;
                                            AlguiMemTool.setMemoryAddrValue(speedValue, daddr, AlguiMemTool.TYPE_FLOAT, true);
                                            result = "🎉 E服加速开启成功！倍数: " + speedValue;
                                        } else {
                                            // 关闭E服加速，恢复默认值
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm.erolabs");
                                            long sAddr = AlguiMemTool.getModuleBaseAddr("libunity.so:bss", AlguiMemTool.HEAD_CB);
                                            long daddr = AlguiMemTool.jump64(sAddr + 0x245E8) + 0xFC;
                                            AlguiMemTool.setMemoryAddrValue("1f", daddr, AlguiMemTool.TYPE_FLOAT, false);
                                            result = "🔒 E服加速已关闭";
                                        }
                                    } catch (Exception e) {
                                        result = "❌ E服加速操作失败: " + e.getMessage();
                                    }

                                    // 切换回主线程显示结果
                                    String finalResult = result;
                                    mainHandler.post(() -> {
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "E服加速", finalResult, 3);
                                    });
                                });
                                speedThread.start();
                            }
                        });

                /**
                 * @desc 官服加速功能卡片，可自定义数值
                 */
                AlguiLinearLayout cardOfficialSpeed = new AlguiLinearLayout(aContext);
                cardOfficialSpeed.setOrientation(LinearLayout.VERTICAL);
                cardOfficialSpeed.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardOfficialSpeed.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardOfficialSpeed.setCatRadiu(12); // 圆角
                cardOfficialSpeed.setCatPadding(15, 15, 15, 15); // 内边距
                cardOfficialSpeed.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardOfficialSpeed);

                // 官服加速标题
                a.TextTitle(cardOfficialSpeed, "⚡ 官服加速")
                        .setCatTextSize(11)
                        .setCatTextColor(0xFF2196F3)
                        .setCatPadding(0, 0, 0, 10);

                // 官服加速数值输入框
                final AlguiViewInputBox[] officialSpeedInput = new AlguiViewInputBox[1];
                officialSpeedInput[0] = a.InputFloat(cardOfficialSpeed, "输入加速倍数 (如: 3.0)", "应用")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                        .setCatInputText("3.0") // 默认值
                        .setCatInputTextColor(0xFFFFFFFF)
                        .setCatButtonBackColor(0xFF2196F3)
                        .setCatButtonTextColor(0xFFFFFFFF)
                        .setCatMargins(0, 0, 0, 10);
                officialSpeedInput[0].getByteEditText().setBackgroundColor(0x40FFFFFF);

                final boolean[] officialSpeedSwitch = {false};
                final AlguiViewButton[] btnOfficialSpeed = new AlguiViewButton[1];
                btnOfficialSpeed[0] = a.Button(cardOfficialSpeed, "⚡ 官服加速(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                officialSpeedSwitch[0] = !officialSpeedSwitch[0];
                                if (officialSpeedSwitch[0]) {
                                    btnOfficialSpeed[0].setCatText("⚡ 官服加速(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnOfficialSpeed[0].setCatText("⚡ 官服加速(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                // 使用现代线程方式替代AsyncTask
                                Handler mainHandler = new Handler(Looper.getMainLooper());
                                Thread speedThread = new Thread(() -> {
                                    String result;
                                    try {
                                        String speedValue = officialSpeedInput[0].getByteEditText().getText().toString().trim();
                                        if (speedValue.isEmpty()) {
                                            speedValue = "3.0"; // 默认值
                                        }

                                        if (officialSpeedSwitch[0]) {
                                            // 开启官服加速
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm");
                                            long sAddr = AlguiMemTool.getModuleBaseAddr("libunity.so:bss", AlguiMemTool.HEAD_CB);
                                            long daddr = AlguiMemTool.jump64(sAddr + 0x245E8) + 0xFC;
                                            AlguiMemTool.setMemoryAddrValue(speedValue, daddr, AlguiMemTool.TYPE_FLOAT, true);
                                            result = "🎉 官服加速开启成功！倍数: " + speedValue;
                                        } else {
                                            // 关闭官服加速，恢复默认值
                                            AlguiMemTool.setPackageName("com.pinkcore.tkfm");
                                            long sAddr = AlguiMemTool.getModuleBaseAddr("libunity.so:bss", AlguiMemTool.HEAD_CB);
                                            long daddr = AlguiMemTool.jump64(sAddr + 0x245E8) + 0xFC;
                                            AlguiMemTool.setMemoryAddrValue("1f", daddr, AlguiMemTool.TYPE_FLOAT, false);
                                            result = "🔒 官服加速已关闭";
                                        }
                                    } catch (Exception e) {
                                        result = "❌ 官服加速操作失败: " + e.getMessage();
                                    }

                                    // 切换回主线程显示结果
                                    String finalResult = result;
                                    mainHandler.post(() -> {
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "官服加速", finalResult, 3);
                                    });
                                });
                                speedThread.start();
                            }
                        });

                //绘制静态视图到屏幕上
                a.WinDraw(
                        a.TextTag(null, "Thoka辅助 联系QQ2647103221购买 [到期时间：%s]", 0xCE000000, expireTime)
                                .setCatTextSize(8)
                                .setCatTextColor(0xFFFFFFFF),//绘制的视图
                        Gravity.BOTTOM | Gravity.START,//坐标原点 (这里右上原点)
                        10, 10,//相对原点xy偏移
                        false//视图是否可接收触摸事件
                );
            }
        }.execute();
    }

    /* Algui Main */
    private Game2FloatWindow() {
        throw new UnsupportedOperationException("cannot be instantiated");
    }
    public static final String TAG = "Game2FloatWindow";
    public static Context aContext;

    public static void start(Context c) {
        aContext = c;
        a = new AlguiToolApp(); // 修改为无参构造函数调用
        if (is2FA) {
            Net2FA();
        } else {
            MyMenu("免费", "无限期", new HashMap<String, String>());
        }
        // 初始化网络验证
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity).initNet();
    }

    /**
     * 检查远程控制变量
     */
    private static void checkRemoteControlVariables(HashMap<String, String> remoteVars, String kami) {
        if (remoteVars == null) {
            return;
        }

        // 1. 检查强制退出开关 (killswitch)
        String killSwitch = remoteVars.get("killswitch");
        if ("exit".equals(killSwitch) || "kill".equals(killSwitch) || "stop".equals(killSwitch)) {
            showExitMessage("检测到异常使用，应用即将关闭！");
            System.exit(0);
        }

        // 2. 检查用户黑名单 (blackuser)
        String blacklistUser = remoteVars.get("blackuser");
        if (blacklistUser != null && !blacklistUser.isEmpty() && !"none".equals(blacklistUser)) {
            String[] blackUsers = blacklistUser.split(",");
            for (String user : blackUsers) {
                if (user.trim().equals(kami)) {
                    showExitMessage("账户已被封禁，请联系客服！");
                    System.exit(0);
                }
            }
        }

        // 3. 检查设备黑名单 (blackdevice)
        String blacklistDevice = remoteVars.get("blackdevice");
        if (blacklistDevice != null && !blacklistDevice.isEmpty() && !"none".equals(blacklistDevice)) {
            String deviceId = android.provider.Settings.Secure.getString(aContext.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);
            String[] blackDevices = blacklistDevice.split(",");
            for (String device : blackDevices) {
                if (device.trim().equals(deviceId)) {
                    showExitMessage("设备已被封禁，请联系客服！");
                    System.exit(0);
                }
            }
        }

        // 4. 检查功能禁用 (disablefunc)
        String disableFunc = remoteVars.get("disablefunc");
        if (disableFunc != null && !disableFunc.isEmpty() && !"none".equals(disableFunc)) {
            if (disableFunc.contains("game2")) {
                showExitMessage("当前功能已被禁用！");
                System.exit(0);
            }
        }

        // 5. 检查强制更新 (forceupdate)
        String forceUpdate = remoteVars.get("forceupdate");
        if ("update".equals(forceUpdate) || "force".equals(forceUpdate)) {
            showForceUpdateDialog();
        }

        // 6. 显示警告消息 (warningmsg)
        String warningMsg = remoteVars.get("warningmsg");
        if (warningMsg != null && !warningMsg.isEmpty() && !"none".equals(warningMsg)) {
            AlguiWinInform.Get(aContext).showInfo_White(
                    AlguiAssets.Icon.inform_info,
                    "系统通知",
                    warningMsg,
                    5
            );
        }

        // 7. 启用反破解保护 (anticrack)
        String antiCrack = remoteVars.get("anticrack");
        if ("enable".equals(antiCrack) || "on".equals(antiCrack)) {
            // 启动反破解保护，每30秒检查一次
            AlguiAntiCrack.startProtection(aContext, 30);
        }
    }

    /**
     * 显示退出消息
     */
    private static void showExitMessage(String message) {
        AlguiWinInform.Get(aContext).showInfo_White(
                AlguiAssets.Icon.inform_info,
                "系统通知",
                message,
                3
        );
        // 直接退出
        System.exit(0);
    }

    /**
     * 显示强制更新对话框
     */
    private static void showForceUpdateDialog() {
        AlguiWinInform.Get(aContext).showInfo_White(
                AlguiAssets.Icon.inform_info,
                "强制更新",
                "检测到新版本，请立即更新！",
                3
        );
        // 直接退出
        System.exit(0);
    }
}
