package com.bytecat.algui;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.Nullable;
import android.graphics.drawable.Drawable;

import com.bytecat.algui.AlguiTools.AlguiToolResource;

/**
 * 游戏详情页Activity，展示游戏信息、版本选择和下载功能。
 */
public class GameDetailActivity extends Activity {

    private ImageView imgGameLogo;
    private TextView txtGameName;
    private RadioGroup radioGroupVersion;
    private Button btnDownload, btnBack;

    // 下载链接配置
    private static final String URL_XYJH_E = "https://www.123912.com/s/QaJtVv-kUHP";
    private static final String URL_XYJH_H = "https://www.123865.com/s/QaJtVv-kUHP";
    private static final String URL_TXBM_E = "https://www.123912.com/s/QaJtVv-9UHP";
    private static final String URL_TXBM_H = "https://www.123912.com/s/QaJtVv-QUHP";
    private static final String URL_PJYYWL_E = "https://www.123912.com/s/QaJtVv-AUHP";
    private static final String URL_PJYYWL_H = "https://www.123865.com/s/QaJtVv-AUHP";
    private static final String URL_XZTM_E = "https://www.123912.com/s/QaJtVv-HUHP";
    private static final String URL_XZTM_H = "https://www.123912.com/s/QaJtVv-hUHP";
    private static final String URL_YJWY_E = "https://www.123912.com/s/QaJtVv-3UHP";
    private static final String URL_YJWY_H = "https://www.123865.com/s/QaJtVv-3UHP";

    private String gameKey = "";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_game_detail);

        imgGameLogo = findViewById(R.id.imgGameLogo);
        txtGameName = findViewById(R.id.txtGameName);
        radioGroupVersion = findViewById(R.id.radioGroupVersion);
        btnDownload = findViewById(R.id.btnDownload);
        btnBack = findViewById(R.id.btnBack);

        // 获取传递的游戏名
        Intent intent = getIntent();
        String gameName = intent.getStringExtra("gameName");
        if (gameName == null) {
            gameName = "星陨计划";
        }
        txtGameName.setText(gameName);

        // 根据游戏名设置图片
        switch (gameName) {
            case "星陨计划":
                setGameLogo("xyjh");
                gameKey = "xyjh";
                break;
            case "天下布魔":
                setGameLogo("txbm");
                gameKey = "txbm";
                break;
            case "潘吉亚异闻录":
                setGameLogo("pjyywl");
                gameKey = "pjyywl";
                break;
            case "贤者同盟":
                setGameLogo("xztm");
                gameKey = "xztm";
                break;
            case "樱井物语":
                setGameLogo("yjwy");
                gameKey = "yjwy";
                break;
            default:
                setGameLogo("ic_launcher");
                gameKey = "default";
                break;
        }

        // 返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 添加RadioGroup监听器来调试
        radioGroupVersion.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.radioE) {
                System.out.println("选择了E服版本");
            } else if (checkedId == R.id.radioH) {
                System.out.println("选择了工口服版本");
            }
        });

        // 下载按钮
        btnDownload.setOnClickListener(v -> {
            int checkedId = radioGroupVersion.getCheckedRadioButtonId();
            if (checkedId == -1) {
                Toast.makeText(this, "请选择版本", Toast.LENGTH_SHORT).show();
                return;
            }
            String url = getDownloadUrl(gameKey, checkedId == R.id.radioE ? "E" : "H");
            if (url == null) {
                Toast.makeText(this, "暂无下载链接", Toast.LENGTH_SHORT).show();
                return;
            }
            // 跳转到浏览器下载
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(browserIntent);
        });
    }

    /**
     * 获取下载链接
     *
     * @param key 游戏key
     * @param version 版本（E/H）
     * @return 下载链接
     */
    private String getDownloadUrl(String key, String version) {
        switch (key) {
            case "xyjh":
                return version.equals("E") ? URL_XYJH_E : URL_XYJH_H;
            case "txbm":
                return version.equals("E") ? URL_TXBM_E : URL_TXBM_H;
            case "pjyywl":
                return version.equals("E") ? URL_PJYYWL_E : URL_PJYYWL_H;
            case "xztm":
                return version.equals("E") ? URL_XZTM_E : URL_XZTM_H;
            case "yjwy":
                return version.equals("E") ? URL_YJWY_E : URL_YJWY_H;
        }
        return null;
    }

    /**
     * 安全地设置游戏logo图片
     * @param drawableName 图片资源名称
     */
    private void setGameLogo(String drawableName) {
        try {
            // 尝试从assets目录加载图片
            String fileName = AlguiToolResource.getDrawableFileName(drawableName);
            Drawable drawable = AlguiToolResource.loadDrawableFromAssets(this, fileName);
            if (drawable != null) {
                imgGameLogo.setImageDrawable(drawable);
                System.out.println("游戏logo从assets加载: " + drawableName);
            } else {
                // 如果assets中没有，尝试使用传统方式
                switch (drawableName) {
                    case "xyjh":
                        imgGameLogo.setImageResource(R.drawable.xyjh);
                        break;
                    case "txbm":
                        imgGameLogo.setImageResource(R.drawable.txbm);
                        break;
                    case "pjyywl":
                        imgGameLogo.setImageResource(R.drawable.pjyywl);
                        break;
                    case "xztm":
                        imgGameLogo.setImageResource(R.drawable.xztm);
                        break;
                    case "yjwy":
                        imgGameLogo.setImageResource(R.drawable.yjwy);
                        break;
                    case "ic_launcher":
                        imgGameLogo.setImageResource(R.drawable.ic_launcher);
                        break;
                    default:
                        imgGameLogo.setImageResource(R.drawable.ic_launcher);
                        break;
                }
                System.out.println("游戏logo从res加载: " + drawableName);
            }
        } catch (Exception e) {
            System.out.println("设置游戏logo失败: " + drawableName + ", 错误: " + e.getMessage());
            // 使用默认图标
            try {
                imgGameLogo.setImageResource(R.drawable.ic_launcher);
            } catch (Exception ex) {
                System.out.println("默认图标也加载失败: " + ex.getMessage());
            }
        }
    }
}
