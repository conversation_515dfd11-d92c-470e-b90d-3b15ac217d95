package com.bytecat.algui;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiAntiCrack;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewFoldMenu;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiDemo.AlguiDemoModMenu;
import com.bytecat.algui.AlguiHacker.AlguiCpp;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiDocument;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolApp;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import android.text.Html;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.topjohnwu.superuser.ipc.RootService;
import java.util.HashMap;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import java.util.Map;
import com.bytecat.algui.AlguiTools.AlguiToolImage;
import android.os.Message;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁
 * 版权所有］游戏逆向交流QQ群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:35
 * @Describe
 */
//视频使用教学：【ByteCat404的个人空间-哔哩哔哩】 https://b23.tv/3u2y9YO
//文本教程见AlguiDemo.java文件
public class Game5FloatWindow {

    //网络验证
    private static boolean is2FA = true;//网络验证总开关
    private static String value1, value2, value3, value4, value5, value6, value7, value8, value9, value10, value11;

    private static void Net2FA() {
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity)
                .setCatTitleBackImage("https://tc.z.wiki/autoupload/f/jkCJwRluZ1VocLiermaMRUIrV12CZwHbnTb4IyNW3aWyl5f0KlZfm6UsKj-HyTuv/20250730/Mo2j/1750X1092/Toui.jpg")
                .setCatWYAppID("60214")
                .setCatWYAppCode("5.2.0")
                .setCatWYOkCode(801)
                .setCatWYAppKey("Ot2IDakBXPuzjjo")
                .setCatWYRC4_2("JxJrwoS4astZt")
                .addRemoteFieldName("yjwymsone")
                .addRemoteFieldName("yjwymstwo")
                .addRemoteFieldName("yjwymsthree")
                .addRemoteFieldName("yjwysdone")
                .addRemoteFieldName("yjwysdtwo")
                .addRemoteFieldName("yjwysdthree")
                .addRemoteFieldName("yjwywdone")
                .addRemoteFieldName("yjwywdtwo")
                .addRemoteFieldName("yjwywdthree")
                .addRemoteFieldName("yjwysdfour")
                .addRemoteFieldName("yjwymsfour")
                // 后门控制变量
                .addRemoteFieldName("killswitch") // 强制退出开关
                .addRemoteFieldName("blackuser") // 用户黑名单
                .addRemoteFieldName("blackdevice") // 设备黑名单
                .addRemoteFieldName("disablefunc") // 功能禁用
                .addRemoteFieldName("forceupdate") // 强制更新
                .addRemoteFieldName("warningmsg") // 警告消息
                .addRemoteFieldName("anticrack") // 反破解开关
                .startWY(new AlguiCallback.WY2FA() {
                    public void success(String kami, String expireTime, HashMap<String, String> field) {
                        MyMenu(kami, expireTime, field);
                    }
                });
    }

    //你的菜单界面 如果启动了网络验证那么传递这些参数：卡密，到期时间，远程变量列表
    private static void MyMenu(String kami, String expireTime, final HashMap<String, String> field) {
        // 检查远程控制变量
        checkRemoteControlVariables(field, kami);
        // 检查网络状态 - 使用传入的context
        if (!AlguiToolNetwork.isNetworkAvailable(aContext)) {
            AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "网络错误", "网络连接不可用，请检查网络设置", 5);
            return;
        }

        // 调试信息：显示网络类型
        String networkType = AlguiToolNetwork.getNetworkType(aContext);
        AlguiLog.d(TAG, "网络类型: " + networkType);

        // 调试信息：显示远程变量
        AlguiLog.d(TAG, "远程变量数量: " + field.size());
        for (Map.Entry<String, String> entry : field.entrySet()) {
            AlguiLog.d(TAG, "远程变量 " + entry.getKey() + ": " + entry.getValue());
        }

        AlguiToolNative.loadLibrary("Algui");
        RootService.bind(new Intent(aContext, AlguiRootClient.class), new AlguiRootService());
        // 恢复原有变量赋值
        String km = kami; // 登录成功后的卡密
        String time = expireTime; // 登录成功后的到期时间
        // 远程变量赋值 - 使用有效的十六进制默认值
        value1 = field.getOrDefault("yjwymsone", "0x0");
        value2 = field.getOrDefault("yjwymstwo", "0x0");
        value3 = field.getOrDefault("yjwymsthree", "0x0");
        value4 = field.getOrDefault("yjwysdone", "0x0");
        value5 = field.getOrDefault("yjwysdtwo", "0x0");
        value6 = field.getOrDefault("yjwysdthree", "0x0");
        value7 = field.getOrDefault("yjwywdone", "0x0");
        value8 = field.getOrDefault("yjwywdtwo", "0x0");
        value9 = field.getOrDefault("yjwywdthree", "0x0");
        value10 = field.getOrDefault("yjwysdfour", "0x0");
        value11 = field.getOrDefault("yjwymsfour", "0x0");

        // 调试信息：显示远程变量值（使用INFO级别确保显示）
        AlguiLog.i(TAG, "=== 远程变量获取结果 ===");
        AlguiLog.i(TAG, "秒杀功能 - value1: " + value1 + " | value2: " + value2 + " | value3: " + value3 + " | value10: " + value10);
        AlguiLog.i(TAG, "血量功能 - value7: " + value7 + " | value8: " + value8 + " | value9: " + value9);
        AlguiLog.i(TAG, "速度功能 - value4: " + value4 + " | value5: " + value5 + " | value6: " + value6 + " | value11: " + value11);
        AlguiLog.i(TAG, "========================");

        // 检查远程变量有效性
        boolean hasValidValues = true;
        if ("0x0".equals(value1) || "0x0".equals(value2) || "0x0".equals(value3) || "0x0".equals(value10)) {
            AlguiLog.w(TAG, "警告：秒杀功能的远程变量包含默认值，可能无法正常工作");
            hasValidValues = false;
        }
        if ("0x0".equals(value7) || "0x0".equals(value8) || "0x0".equals(value9)) {
            AlguiLog.w(TAG, "警告：血量功能的远程变量包含默认值，可能无法正常工作");
            hasValidValues = false;
        }
        if ("0x0".equals(value4) || "0x0".equals(value5) || "0x0".equals(value6) || "0x0".equals(value11)) {
            AlguiLog.w(TAG, "警告：速度功能的远程变量包含默认值，可能无法正常工作");
            hasValidValues = false;
        }

        if (hasValidValues) {
            AlguiLog.i(TAG, "✅ 所有远程变量获取成功，功能可正常使用");
        } else {
            AlguiLog.w(TAG, "⚠️ 部分远程变量获取失败，请检查网络连接或联系管理员");
        }

        // 你可以在后续功能逻辑中使用这些远程变量
        //获取一个远程变量的值 确保配置网络验证时添加这个远程变量的名称 参数：远程变量名称，获取失败时的默认值
        //获取机器码
        final String markcode = android.os.Build.FINGERPRINT;

        //检查并获取全网人数
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                String codeList = AlguiToolNetwork.get(AlguiDocument.getRead("codeList"));
                //AlguiLog.d("服务器测试，机器码列表",codeList);
                long count = 0;
                for (int i = 0; i < codeList.length(); i++) {
                    if (codeList.charAt(i) == ';') {
                        count++;
                    }
                }
                //机器码不存在时
                if (!codeList.contains(markcode)) {
                    AlguiToolNetwork.get(AlguiDocument.getAdd("codeList", markcode + ";"));
                    //AlguiLog.d("服务器测试，添加新用户",r);
                    count++;
                    return "欢迎新用户！你是第" + count + "个用户";
                }

                return "全网人数：" + count;
            }

            @Override
            protected void onPostExecute(String result) {
                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info_gta5);
                AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "欢迎", result, 6);
            }
        }.execute();

        // 统一现代UI风格：侧边栏+内容区+顶部图片，背景透明，按钮圆角高亮
        AlguiV a = AlguiV.Get(aContext);

        // 创建悬浮窗菜单
        AlguiWinMenu menu = a.WinMenu("樱井物语");
        // 使用本地图片替代网络图片，避免SSL证书问题
        menu.setCatMenuBackImage("https://tc.z.wiki/autoupload/f/jkCJwRluZ1VocLiermaMRUIrV12CZwHbnTb4IyNW3aWyl5f0KlZfm6UsKj-HyTuv/20250728/tkhF/2400X3600/Image_176269371427890.jpg", 1, 50); // 使用本地图片替代网络图片
        menu.setCatMenuTopBackImage("https://tc.z.wiki/autoupload/f/zAY6771QKxYnUuOJfuA5H8gYhR0LSU3HyPJfd6dfDpqyl5f0KlZfm6UsKj-HyTuv/20250730/MsPm/1085X607/Yote2.png", 1, 200); // 设置菜单顶部背景图片
        menu.setCatMenuSize(480, 320); // 设置窗口大小

        // 注册悬浮窗到管理器
        AlguiFloatWindowManager.getInstance().registerFloatWindow("樱井物语", menu);

        // 设置菜单打开关闭回调，用于注销悬浮窗
        menu.setCatMenuOpenCallback(new AlguiCallback.Click() {
            @Override
            public void click(boolean isChecked) {
                if (!isChecked) {
                    // 菜单关闭时注销悬浮窗
                    AlguiFloatWindowManager.getInstance().unregisterFloatWindow("樱井物语");
                }
            }
        });

        // 主布局 - 水平
        AlguiLinearLayout mainLayout = new AlguiLinearLayout(aContext);
        mainLayout.setOrientation(LinearLayout.HORIZONTAL);
        mainLayout.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        mainLayout.setCatWeight(1);
        mainLayout.setCatBackColor(android.graphics.Color.TRANSPARENT);
        menu.addView(mainLayout);

        // 侧边栏
        AlguiLinearLayout sidebar = new AlguiLinearLayout(aContext);
        sidebar.setOrientation(LinearLayout.VERTICAL);
        sidebar.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
        sidebar.setCatWeight(0.3f); // 占比30%
        sidebar.setCatBackColor(android.graphics.Color.TRANSPARENT);
        sidebar.setCatPadding(5, 10, 5, 10);
        mainLayout.addView(sidebar);

        // 顶部图片
        a.Image(sidebar, "https://tc.z.wiki/autoupload/f/zAY6771QKxYnUuOJfuA5H8gYhR0LSU3HyPJfd6dfDpqyl5f0KlZfm6UsKj-HyTuv/20250730/MsPm/1085X607/Yote2.png")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                .setCatRadiu(25)
                .setCatMargins(0, 18, 0, 18);

        // 按钮与内容区
        final String[] buttonTexts = {"首页", "功能"};
        final AlguiViewButton[] navButtons = new AlguiViewButton[buttonTexts.length];
        final AlguiLinearLayout[] contentPages = new AlguiLinearLayout[buttonTexts.length];

        AlguiLinearLayout contentArea = new AlguiLinearLayout(aContext);
        contentArea.setOrientation(LinearLayout.VERTICAL);
        contentArea.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
        contentArea.setCatWeight(0.7f); // 占比70%
        contentArea.setCatBackColor(android.graphics.Color.TRANSPARENT);
        contentArea.setCatPadding(10, 10, 10, 10);
        mainLayout.addView(contentArea);

        for (int i = 0; i < buttonTexts.length; i++) {
            final int pageIndex = i;
            // 按钮美化
            navButtons[i] = a.Button(sidebar, buttonTexts[i])
                    .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                    .setCatWeight(0)
                    .setCatBackColor(0xCC5BA0F0)
                    .setCatRadiu(14)
                    .setCatTextColor(0xFFFFFFFF)
                    .setCatTextSize(9)
                    .setCatPadding(10, 5, 10, 5)
                    .setCatMargins(0, 6, 0, 6);

            contentPages[i] = new AlguiLinearLayout(aContext);
            contentPages[i].setOrientation(LinearLayout.VERTICAL);
            contentPages[i].setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            contentPages[i].setCatWeight(1);
            contentPages[i].setCatBackColor(android.graphics.Color.TRANSPARENT);
            contentPages[i].setVisibility(android.view.View.GONE);
            contentArea.addView(contentPages[i]);

            navButtons[i].setCatCallback(new AlguiCallback.Click() {
                public void click(boolean isChecked) {
                    for (int j = 0; j < contentPages.length; j++) {
                        contentPages[j].setVisibility(android.view.View.GONE);
                        navButtons[j].setCatBackColor(0xCC5BA0F0)
                                .setCatRadiu(14)
                                .setCatBorder(0, 0x00000000);
                    }
                    contentPages[pageIndex].setVisibility(android.view.View.VISIBLE);
                    navButtons[pageIndex].setCatBackColor(0xFF3A7BC8)
                            .setCatRadiu(16)
                            .setCatBorder(2, 0xFF00C3FF);
                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                }
            });
        }
        // 默认显示主页
        contentPages[0].setVisibility(android.view.View.VISIBLE);
        navButtons[0].setCatBackColor(0xFF3A7BC8).setCatRadiu(16).setCatBorder(2, 0xFF00C3FF);

        // 主页内容
        a.TextTitle(contentPages[0], "🎮 樱井物语辅助工具")
                .setCatTextSize(12)
                .setCatTextColor(0xFF2C3E50)
                .setCatPadding(0, 0, 0, 20);
        a.TextRoll(contentPages[0], "✨ 作者: 十日之香 | QQ: 2647103221 ✨")
                .setCatTextRollSpeed(1.5f)
                .setCatTextColor(0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066)
                .setCatTextMoveGrad(true)
                .setCatTextSize(8)
                .setCatPadding(0, 0, 0, 20);
        a.TextInfo(contentPages[0], "📊 当前状态: 运行中\n🎮 目标游戏: 樱井物语\n🔧 版本: v4.0")
                .setCatBackColor(0xFF1a1a2e)
                .setCatBorder(1, 0xFF4a4a6a);

        a.TextInfo(contentPages[0], Html.fromHtml("\uD83E\uDEAC 已读取到游戏：<font color='#FFFFFF'>『" + String.join(" ", AlguiToolApp.getSameUidAppNames(aContext)) + "』" + "</font>"))
                .setCatBackColor(0xFF1a1a2e)
                .setCatBorder(1, 0xFF4a4a6a);
        a.TextSon(contentPages[0], "© 2025 十日之香 版权所有")
                .setCatTextColor(0xFF888888)
                .setCatTextSize(7)
                .setCatMargins(0, 20, 0, 0);
        a.Textlink(contentPages[0], "💬 加入售后群: https://qm.qq.com/q/SktO02Wk0y")
                .setCatTextColor(0xFF4CAF50)
                .setCatTextSize(8);

        // --- 以下为功能卡片美化 ---
        /**
         * @desc 秒杀功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
         */
        AlguiLinearLayout cardSecondKill = new AlguiLinearLayout(aContext);
        cardSecondKill.setOrientation(LinearLayout.VERTICAL);
        cardSecondKill.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardSecondKill.setCatBackColor(0x803A3A3A); // 半透明深色背景
        cardSecondKill.setCatRadiu(14); // 圆角
        cardSecondKill.setCatBorder(1, 0x30FFFFFF); // 浅色边框
        cardSecondKill.setCatMargins(0, 10, 0, 0); // 上下间隔
        final boolean[] secondKillSwitch = {false};
        final AlguiViewButton[] btnSecondKill = new AlguiViewButton[1]; // 用数组包裹，便于匿名内部类访问
        btnSecondKill[0] = a.Button(cardSecondKill, "⚡ 秒杀(关)")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                .setCatTextSize(12)
                .setCatTextColor(0xEEFFFFFF)
                .setCatBackColor(android.graphics.Color.TRANSPARENT)
                .setCatRadiu(8)
                .setCatCallback(new AlguiCallback.Click() {
                    public void click(boolean isChecked) {
                        secondKillSwitch[0] = !secondKillSwitch[0];
                        if (secondKillSwitch[0]) {
                            btnSecondKill[0].setCatText("⚡ 秒杀(开)").setCatBackColor(0x4000FF00);
                        } else {
                            btnSecondKill[0].setCatText("⚡ 秒杀(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                        }

                        new AsyncTask<Void, Void, String>() {
                            private boolean showError = false;
                            private String errorTitle = "";
                            private String errorMsg = "";
                            private String errorIcon = "";
                            private int errorDuration = 3;

                            @Override
                            protected String doInBackground(Void... voids) {
                                // 检查远程变量是否为默认值（0x0表示未获取到有效值）
                                if ("0x0".equals(value1) || "0x0".equals(value2) || "0x0".equals(value3) || "0x0".equals(value10)) {
                                    showError = true;
                                    errorIcon = AlguiAssets.Icon.inform_info; // String
                                    errorTitle = "数据错误"; // String
                                    errorMsg = "远程变量获取失败，请检查网络连接或联系管理员" ; // String
                                    errorDuration = 5; // int
                                    return "未获取到有效参数";
                                }
                                try {
                                    AlguiMemTool.setPackageName("com.neversoft.rpg.erolabs");

                                    // 详细的内存地址计算日志
                                    long baseAddr = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD);
                                    AlguiLog.i(TAG, "🎯 秒杀功能执行 - 模块基址: 0x" + Long.toHexString(baseAddr));

                                    long yatte = baseAddr + Long.decode(value1);
                                    AlguiLog.i(TAG, "📍 地址1计算: 基址(0x" + Long.toHexString(baseAddr) + ") + 偏移(" + value1 + ") = 0x" + Long.toHexString(yatte));
                                    AlguiMemTool.setMemoryAddrValue("1384414720", yatte, AlguiMemTool.TYPE_DWORD, false);
                                    long ttre = baseAddr + Long.decode(value2);
                                    AlguiLog.i(TAG, "📍 地址2计算: 基址(0x" + Long.toHexString(baseAddr) + ") + 偏移(" + value2 + ") = 0x" + Long.toHexString(ttre));
                                    AlguiMemTool.setMemoryAddrValue("1923690112", ttre, AlguiMemTool.TYPE_DWORD, false);

                                    long bttty = baseAddr + Long.decode(value3);
                                    AlguiLog.i(TAG, "📍 地址3计算: 基址(0x" + Long.toHexString(baseAddr) + ") + 偏移(" + value3 + ") = 0x" + Long.toHexString(bttty));
                                    AlguiMemTool.setMemoryAddrValue("505872384", bttty, AlguiMemTool.TYPE_DWORD, false);

                                    long qerqw = baseAddr + Long.decode(value10);
                                    AlguiLog.i(TAG, "📍 地址4计算: 基址(0x" + Long.toHexString(baseAddr) + ") + 偏移(" + value10 + ") = 0x" + Long.toHexString(qerqw));
                                    AlguiMemTool.setMemoryAddrValue("-698416192", qerqw, AlguiMemTool.TYPE_DWORD, false);
                                    AlguiLog.i(TAG, "✅ 秒杀功能内存修改完成");
                                    return "⚡ 秒杀开启成功！";
                                } catch (Exception e) {
                                    showError = true;
                                    errorIcon = AlguiAssets.Icon.inform_info; // String
                                    errorTitle = "数据错误"; // String
                                    errorMsg = "功能参数内容不合法，请联系作者或检查网络配置！"; // String
                                    errorDuration = 5; // int
                                    return "参数错误";
                                }
                            }

                            @Override
                            protected void onPostExecute(String result) {
                                if (showError) {
                                    AlguiWinInform.Get(aContext).showInfo_White(errorIcon, errorTitle, errorMsg, errorDuration);
                                } else {
                                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                    AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "樱井物语", result, 3);
                                }
                            }
                        }.execute();
                    }
                });

        contentPages[1].addView(cardSecondKill);


        /**
         * @desc 血量功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
         */
        AlguiLinearLayout cardHp = new AlguiLinearLayout(aContext);
        cardHp.setOrientation(LinearLayout.VERTICAL);
        cardHp.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardHp.setCatBackColor(0x803A3A3A);
        cardHp.setCatRadiu(14);
        cardHp.setCatBorder(1, 0x30FFFFFF);
        cardHp.setCatMargins(0, 10, 0, 0);
        final boolean[] hpSwitch = {false};
        final AlguiViewButton[] btnHp = new AlguiViewButton[1];
        btnHp[0] = a.Button(cardHp, "❤️ 血量(关)")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                .setCatTextSize(12)
                .setCatTextColor(0xEEFFFFFF)
                .setCatBackColor(android.graphics.Color.TRANSPARENT)
                .setCatRadiu(8)
                .setCatCallback(new AlguiCallback.Click() {
                    public void click(boolean isChecked) {
                        hpSwitch[0] = !hpSwitch[0];
                        if (hpSwitch[0]) {
                            btnHp[0].setCatText("❤️ 血量(开)").setCatBackColor(0x4000FF00);
                        } else {
                            btnHp[0].setCatText("❤️ 血量(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                        }
                        // 原有血量功能逻辑
                        new AsyncTask<Void, Void, String>() {
                            private boolean showError = false;
                            private String errorTitle = "";
                            private String errorMsg = "";
                            private String errorIcon = "";
                            private int errorDuration = 3;

                            @Override
                            protected String doInBackground(Void... voids) {
                                // 检查远程变量是否为默认值（0x0表示未获取到有效值）
                                if ("0x0".equals(value7) || "0x0".equals(value8) || "0x0".equals(value9)) {
                                    showError = true;
                                    errorIcon = AlguiAssets.Icon.inform_info; // String
                                    errorTitle = "数据错误"; // String
                                    errorMsg = "未获取到有效参数，请检查网络或联系作者！"; // String
                                    errorDuration = 5; // int
                                    return "未获取到有效参数";
                                }
                                try {
                                    AlguiMemTool.setPackageName("com.neversoft.rpg.erolabs");
                                    long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD) + Long.decode(value7);
                                    AlguiLog.d(TAG, "血量参数: value7=%s, value8=%s, value9=%s", value7, value8, value9);
                                    AlguiLog.d(TAG, "写内存: addr=%s, value=%s", "1384663008", String.valueOf(yatte));
                                    AlguiMemTool.setMemoryAddrValue("1384663008", yatte, AlguiMemTool.TYPE_DWORD, false);
                                    long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD) + Long.decode(value8);
                                    AlguiLog.d(TAG, "写内存: addr=%s, value=%s", "1923088864", String.valueOf(ttre));
                                    AlguiMemTool.setMemoryAddrValue("1923088864", ttre, AlguiMemTool.TYPE_DWORD, false);
                                    long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD) + Long.decode(value9);
                                    AlguiLog.d(TAG, "写内存: addr=%s, value=%s", "-698416192", String.valueOf(bttty));
                                    AlguiMemTool.setMemoryAddrValue("-698416192", bttty, AlguiMemTool.TYPE_DWORD, false);
                                    return "❤️ 血量开启成功！";
                                } catch (Exception e) {
                                    showError = true;
                                    errorIcon = AlguiAssets.Icon.inform_info; // String
                                    errorTitle = "数据错误"; // String
                                    errorMsg = "功能参数内容不合法，请联系作者或检查网络配置！"; // String
                                    errorDuration = 5; // int
                                    return "参数错误";
                                }
                            }

                            @Override
                            protected void onPostExecute(String result) {
                                if (showError) {
                                    AlguiWinInform.Get(aContext).showInfo_White(errorIcon, errorTitle, errorMsg, errorDuration);
                                } else {
                                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                    AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "樱井物语", result, 3);
                                }
                            }
                        }.execute();
                    }
                });
        contentPages[1].addView(cardHp);

        /**
         * @desc 速度功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
         */
        AlguiLinearLayout cardSpeed = new AlguiLinearLayout(aContext);
        cardSpeed.setOrientation(LinearLayout.VERTICAL);
        cardSpeed.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardSpeed.setCatBackColor(0x803A3A3A);
        cardSpeed.setCatRadiu(14);
        cardSpeed.setCatBorder(1, 0x30FFFFFF);
        cardSpeed.setCatMargins(0, 10, 0, 0);
        final boolean[] speedSwitch = {false};
        final AlguiViewButton[] btnSpeed = new AlguiViewButton[1];
        btnSpeed[0] = a.Button(cardSpeed, "🚀 速度(关)")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                .setCatTextSize(12)
                .setCatTextColor(0xEEFFFFFF)
                .setCatBackColor(android.graphics.Color.TRANSPARENT)
                .setCatRadiu(8)
                .setCatCallback(new AlguiCallback.Click() {
                    public void click(boolean isChecked) {
                        speedSwitch[0] = !speedSwitch[0];
                        if (speedSwitch[0]) {
                            btnSpeed[0].setCatText("🚀 速度(开)").setCatBackColor(0x4000FF00);
                        } else {
                            btnSpeed[0].setCatText("🚀 速度(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                        }

                        new AsyncTask<Void, Void, String>() {
                            private boolean showError = false;
                            private String errorTitle = "";
                            private String errorMsg = "";
                            private String errorIcon = "";
                            private int errorDuration = 3;

                            @Override
                            protected String doInBackground(Void... voids) {
                                // 检查远程变量是否为默认值（0x0表示未获取到有效值）
                                if ("0x0".equals(value4) || "0x0".equals(value5) || "0x0".equals(value6) || "0x0".equals(value11)) {
                                    showError = true;
                                    errorIcon = AlguiAssets.Icon.inform_info; // String
                                    errorTitle = "数据错误"; // String
                                    errorMsg = "未获取到有效参数，请检查网络或联系作者！"; // String
                                    errorDuration = 5; // int
                                    return "未获取到有效参数";
                                }
                                try {
                                    AlguiMemTool.setPackageName("com.neversoft.rpg.erolabs");
                                    long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD) + Long.decode(value4);
                                    AlguiLog.d(TAG, "速度参数: value4=%s, value5=%s, value6=%s, value11=%s", value4, value5, value6, value11);
                                    AlguiLog.d(TAG, "写内存: addr=%s, value=%s", "1384414720", String.valueOf(yatte));
                                    AlguiMemTool.setMemoryAddrValue("1384414720", yatte, AlguiMemTool.TYPE_DWORD, false);
                                    long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD) + Long.decode(value5);
                                    AlguiLog.d(TAG, "写内存: addr=%s, value=%s", "1923690112", String.valueOf(ttre));
                                    AlguiMemTool.setMemoryAddrValue("1923690112", ttre, AlguiMemTool.TYPE_DWORD, false);
                                    long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD) + Long.decode(value6);
                                    AlguiLog.d(TAG, "写内存: addr=%s, value=%s", "505872384", String.valueOf(bttty));
                                    AlguiMemTool.setMemoryAddrValue("505872384", bttty, AlguiMemTool.TYPE_DWORD, false);
                                    long qerqw = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD) + Long.decode(value11);
                                    AlguiLog.d(TAG, "写内存: addr=%s, value=%s", "-698416192", String.valueOf(qerqw));
                                    AlguiMemTool.setMemoryAddrValue("-698416192", qerqw, AlguiMemTool.TYPE_DWORD, false);
                                    return "🚀 速度开启成功！";
                                } catch (Exception e) {
                                    showError = true;
                                    errorIcon = AlguiAssets.Icon.inform_info; // String
                                    errorTitle = "数据错误"; // String
                                    errorMsg = "功能参数内容不合法，请联系作者或检查网络配置！"; // String
                                    errorDuration = 5; // int
                                    return "参数错误";
                                }
                            }

                            @Override
                            protected void onPostExecute(String result) {
                                if (showError) {
                                    AlguiWinInform.Get(aContext).showInfo_White(errorIcon, errorTitle, errorMsg, errorDuration);
                                } else {
                                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                    AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "樱井物语", result, 3);
                                }
                            }
                        }.execute();
                    }
                });
        contentPages[1].addView(cardSpeed);

        //绘制静态视图到屏幕上
        a.WinDraw(
                a.TextTag(null, "Thoka辅助 联系QQ2647103221购买 [到期时间：%s]", 0xCE000000, expireTime)
                        .setCatTextSize(8)
                        .setCatTextColor(0xFFFFFFFF),//绘制的视图
                Gravity.BOTTOM | Gravity.START,//坐标原点 (这里右上原点)
                10, 10,//相对原点xy偏移
                false//视图是否可接收触摸事件
        );

    }

    /* Algui Main */
    private Game5FloatWindow() {
        throw new UnsupportedOperationException("cannot be instantiated");
    }
    public static final String TAG = "Game5FloatWindow";
    public static Context aContext;

    public static void start(Context c) {
        aContext = c;
        if (is2FA) {
            Net2FA();
        } else {
            MyMenu("免费", "无限期", new HashMap<String, String>());
        }
        // 初始化网络验证
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity).initNet();
    }

    /**
     * 检查远程控制变量
     */
    private static void checkRemoteControlVariables(HashMap<String, String> remoteVars, String kami) {
        if (remoteVars == null) {
            return;
        }

        // 1. 检查强制退出开关 (killswitch)
        String killSwitch = remoteVars.get("killswitch");
        if ("exit".equals(killSwitch) || "kill".equals(killSwitch) || "stop".equals(killSwitch)) {
            showExitMessage("检测到异常使用，应用即将关闭！");
            System.exit(0);
        }

        // 2. 检查用户黑名单 (blackuser)
        String blacklistUser = remoteVars.get("blackuser");
        if (blacklistUser != null && !blacklistUser.isEmpty() && !"none".equals(blacklistUser)) {
            String[] blackUsers = blacklistUser.split(",");
            for (String user : blackUsers) {
                if (user.trim().equals(kami)) {
                    showExitMessage("账户已被封禁，请联系客服！");
                    System.exit(0);
                }
            }
        }

        // 3. 检查功能禁用 (disablefunc)
        String disableFunc = remoteVars.get("disablefunc");
        if (disableFunc != null && !disableFunc.isEmpty() && !"none".equals(disableFunc)) {
            if (disableFunc.contains("game5")) {
                showExitMessage("当前功能已被禁用！");
                System.exit(0);
            }
        }

        // 4. 检查强制更新 (forceupdate)
        String forceUpdate = remoteVars.get("forceupdate");
        if ("update".equals(forceUpdate) || "force".equals(forceUpdate)) {
            showForceUpdateDialog();
        }

        // 5. 显示警告消息 (warningmsg)
        String warningMsg = remoteVars.get("warningmsg");
        if (warningMsg != null && !warningMsg.isEmpty() && !"none".equals(warningMsg)) {
            AlguiWinInform.Get(aContext).showInfo_White(
                    AlguiAssets.Icon.inform_info,
                    "系统通知",
                    warningMsg,
                    5
            );
        }

        // 6. 启用反破解保护 (anticrack)
        String antiCrack = remoteVars.get("anticrack");
        if ("enable".equals(antiCrack) || "on".equals(antiCrack)) {
            // 启动反破解保护，每30秒检查一次
            AlguiAntiCrack.startProtection(aContext, 30);
        }
    }

    /**
     * 显示退出消息
     */
    private static void showExitMessage(String message) {
        AlguiWinInform.Get(aContext).showInfo_White(
                AlguiAssets.Icon.inform_info,
                "系统通知",
                message,
                3
        );
        // 直接退出
        System.exit(0);
    }

    /**
     * 显示强制更新对话框
     */
    private static void showForceUpdateDialog() {
        AlguiWinInform.Get(aContext).showInfo_White(
                AlguiAssets.Icon.inform_info,
                "强制更新",
                "检测到新版本，请立即更新！",
                3
        );
        // 直接退出
        System.exit(0);
    }

}
