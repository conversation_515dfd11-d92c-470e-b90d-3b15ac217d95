# 星落.cs 伤害提升修改指南

## 🎯 修改目标
提高游戏整体伤害输出，通过以下三个方面：
1. 提高攻击类型系数 (CardType相关)
2. 降低防御相关参数 (m_Stiffness)  
3. 提高伤害缩放系数 (scaleOfGreater)

## 📍 具体修改位置和方法

### 1. 攻击类型系数修改

**文件位置**: `星落.cs` 第6696-6701行
**当前代码**:
```csharp
public const enum CardType NONE = 0;           // 无类型
public const enum CardType DEFENSE = 1;        // 防御类型
public const enum CardType MELEE_ATTACK = 2;   // 近战攻击类型
public const enum CardType ASSASSIN = 3;       // 刺客类型
public const enum CardType SUPPORT = 4;        // 支援类型
public const enum CardType RANGED_ATTACK = 5;  // 远程攻击类型
```

**修改建议**:
```csharp
public const enum CardType NONE = 0;           // 无类型 (保持不变)
public const enum CardType DEFENSE = 1;        // 防御类型 (保持不变)
public const enum CardType MELEE_ATTACK = 3;   // 近战攻击 2→3 (+50%伤害)
public const enum CardType ASSASSIN = 5;       // 刺客类型 3→5 (+67%伤害)
public const enum CardType SUPPORT = 4;        // 支援类型 (保持不变)
public const enum CardType RANGED_ATTACK = 7;  // 远程攻击 5→7 (+40%伤害)
```

**修改效果**:
- 近战攻击伤害提升50%
- 刺客攻击伤害提升67%
- 远程攻击伤害提升40%

### 2. 防御参数修改 (m_Stiffness)

**文件位置**: `星落.cs` 第90行和第148行
**当前代码**:
```csharp
// DynamicBone.Particle 类 (第90行)
public System.Single m_Stiffness; // 0x28  //  m刚度

// DynamicBone 主类 (第148行)  
public System.Single m_Stiffness; // 0x50  //  m刚度
```

**修改方法**:
由于这是字段声明，需要在初始化或赋值的地方修改默认值。

**查找初始化位置**:
需要搜索 `m_Stiffness` 的赋值语句，通常在构造函数或初始化方法中。

**建议修改值**:
```csharp
// 原始值可能是 1.0f，建议改为：
m_Stiffness = 0.6f;  // 降低40%，减少防御效果
```

### 3. 伤害缩放系数修改 (scaleOfGreater)

**文件位置**: `星落.cs` 第380行和第419行
**当前代码**:
```csharp
// AudioVisualiser 类字段声明 (第380行)
private System.Single scaleOfGreater; // 0x64  //  更大的比例

// 使用该参数的方法 (第419行)
private System.Void DifferentiateSampleData(System.Single[] samples, System.Single threshold, System.Single scaleOfLess, System.Single scaleOfGreater);
```

**修改方法**:
需要找到 `scaleOfGreater` 的初始化位置或赋值语句。

**建议修改值**:
```csharp
// 如果原始值是 1.0f，建议改为：
scaleOfGreater = 1.5f;  // 提升50%的伤害缩放
// 如果原始值是 1.2f，建议改为：
scaleOfGreater = 1.8f;  // 提升50%的伤害缩放
```

## 🔍 查找具体赋值位置的方法

### 方法1: 搜索赋值语句
```regex
m_Stiffness\s*=\s*[0-9.]+f?
scaleOfGreater\s*=\s*[0-9.]+f?
```

### 方法2: 搜索构造函数
```regex
\.ctor\(\).*{[\s\S]*?m_Stiffness
\.ctor\(\).*{[\s\S]*?scaleOfGreater
```

### 方法3: 搜索初始化方法
```regex
Initialize.*{[\s\S]*?m_Stiffness
Start.*{[\s\S]*?scaleOfGreater
```

## ⚡ 具体修改步骤

### 步骤1: 修改攻击类型系数 ✅ 可直接修改
**位置**: 第6696-6701行
**操作**: 直接修改枚举值

**原始代码**:
```csharp
public const enum CardType MELEE_ATTACK = 2;   // 近战攻击
public const enum CardType ASSASSIN = 3;       // 刺客类型
public const enum CardType RANGED_ATTACK = 5;  // 远程攻击
```

**修改为**:
```csharp
public const enum CardType MELEE_ATTACK = 3;   // 近战攻击 (+50%伤害)
public const enum CardType ASSASSIN = 5;       // 刺客类型 (+67%伤害)
public const enum CardType RANGED_ATTACK = 7;  // 远程攻击 (+40%伤害)
```

### 步骤2: 修改 m_Stiffness ⚠️ 需要找到初始化位置
**问题**: 第90行和第148行只是字段声明，没有默认值
**解决方案**: 需要在以下位置查找并修改初始化值

**查找位置**:
1. **DynamicBone 构造函数** (第231行附近)
2. **DynamicBone.Particle 构造函数** (第107行附近)
3. **Start() 或 Awake() 方法**中的初始化代码

**修改目标**: 将默认刚度值从 1.0f 降低到 0.6f (降低40%)

### 步骤3: 修改 scaleOfGreater ⚠️ 需要找到初始化位置
**问题**: 第380行只是字段声明，第419行是方法参数
**解决方案**: 需要在以下位置查找并修改

**查找位置**:
1. **AudioVisualiser 构造函数** (第421行附近)
2. **Start() 或 Awake() 方法**中的初始化代码
3. **DifferentiateSampleData() 方法调用**时传入的参数值

**修改目标**: 将缩放值从默认值提高50% (例如: 1.2f → 1.8f)

## 🔍 详细查找方法

### 查找 m_Stiffness 初始化
```bash
# 在代码中搜索以下模式:
1. "m_Stiffness = "
2. "Stiffness = "
3. 构造函数中的赋值
4. Start()、Awake()、OnEnable() 方法中的赋值
```

### 查找 scaleOfGreater 初始化
```bash
# 在代码中搜索以下模式:
1. "scaleOfGreater = "
2. "DifferentiateSampleData(" 方法调用
3. AudioVisualiser 相关的初始化代码
```

## 📝 实际修改示例

### 如果找到 m_Stiffness 初始化 (假设在构造函数中):
```csharp
// 原始代码 (假设)
m_Stiffness = 1.0f;

// 修改为
m_Stiffness = 0.6f;  // 降低40%，减少防御效果
```

### 如果找到 scaleOfGreater 初始化:
```csharp
// 原始代码 (假设)
scaleOfGreater = 1.2f;

// 修改为
scaleOfGreater = 1.8f;  // 提高50%，增加伤害缩放
```

### 如果在方法调用中找到:
```csharp
// 原始代码 (假设)
DifferentiateSampleData(samples, 0.5f, 0.8f, 1.2f);

// 修改为
DifferentiateSampleData(samples, 0.5f, 0.8f, 1.8f);  // 最后一个参数是scaleOfGreater
```

## 🎮 预期效果

### 整体伤害提升
- **近战角色**: 伤害提升约60-80%
- **刺客角色**: 伤害提升约80-100%  
- **远程角色**: 伤害提升约50-70%

### 防御效果降低
- **所有角色**: 受到伤害增加约40-60%
- **击退效果**: 更明显的物理反应
- **硬直时间**: 可能略有增加

### 音频触发伤害
- **高强度音频**: 伤害加成提升50%
- **特殊技能**: 触发更强的伤害效果

## ⚠️ 注意事项

1. **备份文件**: 修改前务必备份原始文件
2. **测试平衡**: 修改后需要测试游戏平衡性
3. **逐步调整**: 建议先小幅修改，测试效果后再进一步调整
4. **兼容性**: 确保修改不会导致游戏崩溃或异常

## 🔧 故障排除

### 如果游戏崩溃
1. 检查数值是否过大 (建议不超过原值的2倍)
2. 确认修改的是正确的赋值位置
3. 恢复备份文件重新尝试

### 如果效果不明显
1. 确认修改了所有相关的赋值位置
2. 检查是否有其他限制伤害的机制
3. 尝试进一步提高修改幅度

### 如果平衡性问题
1. 适当降低修改幅度
2. 只修改部分参数进行测试
3. 根据实际游戏体验调整数值
