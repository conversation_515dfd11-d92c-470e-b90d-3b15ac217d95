# 🎮 星落游戏完整伤害系统分析报告

## 📖 概述
本报告基于IL2CPP反编译文件`星落.cs`的深度分析，提取了所有与伤害、攻击、效果相关的类名和字段，供内存修改工具使用。

## 🎯 核心攻击伤害系统

### 1. 攻击类型枚举 (CardType)
**位置**: 第6693行
**内存偏移**: 基础枚举值
```csharp
CardType.NONE = 0              // 无类型
CardType.DEFENSE = 1           // 防御类型  
CardType.MELEE_ATTACK = 3      // 近战攻击 (原值2, +50%伤害)
CardType.ASSASSIN = 5          // 刺客攻击 (原值3, +67%伤害)
CardType.SUPPORT = 4           // 支援类型
CardType.RANGED_ATTACK = 7     // 远程攻击 (原值5, +40%伤害)
```

### 2. 战斗资源管理器 (BattleResourceManager)
**位置**: 第7255行
**功能**: 管理战斗中的资源分配和伤害计算
**用途**: 控制战斗资源分配，影响伤害输出

### 3. 战斗显示系统 (BattleShow)
**位置**: 第7356行
**关键字段**:
```csharp
BattleShow.curEffectObject      // 当前效果对象 (0x28偏移)
BattleShow.battleBurstCamera    // 战斗爆发相机 (0x20偏移)
BattleShow.cachedRenderers      // 缓存渲染器 (0x30偏移)
```
**关键方法**:
```csharp
PlayBurstEffect()    // 播放爆发效果
StopBurstEffect()    // 停止爆发效果
ChangeBattleWave()   // 改变战斗波浪
```

### 4. 子弹效果助手 (BulletEffectHelper)
**位置**: 第7399行
**功能**: 处理子弹伤害效果和碰撞检测
**用途**: 控制远程攻击的伤害计算和视觉效果

### 5. 效果助手 (EffectHelper)
**位置**: 第8121行
**关键字段**:
```csharp
EffectHelper.rotationWithAttackDirection  // 与攻击方向旋转 (0xA8偏移)
```

## 🔧 物理伤害反应系统

### 6. 动态骨骼系统 (DynamicBone)
**位置**: 第138行
**关键字段**:
```csharp
DynamicBone.m_Damping      // 阻尼系数 (0x20偏移) - 影响伤害衰减
DynamicBone.m_Elasticity   // 弹性系数 (0x24偏移) - 影响反弹伤害
DynamicBone.m_Stiffness    // 刚度系数 (0x28偏移) - 影响防御力
DynamicBone.m_Inert        // 惯性系数 (0x2C偏移) - 影响冲击力
DynamicBone.m_Friction     // 摩擦系数 (0x30偏移) - 影响滑动伤害
DynamicBone.m_Radius       // 碰撞半径 (0x34偏移) - 影响攻击范围
```

### 7. 战斗波浪效果 (ChangeBattleWave)
**位置**: 第7442行
**关键字段**:
```csharp
ChangeBattleWave.dissolution_strength  // 溶解强度 (0x30偏移)
ChangeBattleWave.noise_force          // 噪声力度 (0x34偏移)
ChangeBattleWave.blur                 // 模糊程度
```

## 🎵 音频触发伤害系统

### 8. 音频可视化器 (AudioVisualiser)
**位置**: 第362行
**关键字段**:
```csharp
AudioVisualiser.threshold       // 触发阈值 (0x5C偏移)
AudioVisualiser.scaleOfLess     // 低阈值缩放 (0x60偏移)
AudioVisualiser.scaleOfGreater  // 高阈值缩放 (0x64偏移) - 伤害加成
AudioVisualiser.sampleData      // 音频采样数据 (0x28偏移)
AudioVisualiser.sampleSize      // 采样大小 (0x38偏移)
```

## 🎮 输入控制与攻击触发

### 9. 触摸相位控制 (TouchPhase)
**位置**: 第115961行
**关键值**:
```csharp
TouchPhase.Began = 0        // 触摸开始 - 可能触发攻击
TouchPhase.Moved = 1        // 触摸移动 - 可能影响攻击方向
TouchPhase.Ended = 3        // 触摸结束 - 可能释放攻击
```

### 10. 触摸屏效果 (TouchScreenEffect)
**位置**: 第916行
**关键字段**:
```csharp
TouchScreenEffect.effectPath        // 效果路径 (0x18偏移)
TouchScreenEffect.maxCount          // 最大计数 (0x20偏移)
TouchScreenEffect.effectRoot        // 效果根节点 (0x28偏移)
TouchScreenEffect.effectCamera      // 效果相机 (0x38偏移)
TouchScreenEffect.effectList        // 效果列表 (0x40偏移)
```

## ⚡ 粒子系统与生命周期

### 11. 粒子生命周期 (UnityEngine.ParticleSystem.Particle)
**位置**: 第114234行
**关键字段**:
```csharp
Particle.m_Lifetime         // 粒子生命周期 (0x7C偏移)
Particle.m_StartLifetime    // 粒子开始生命周期 (0x80偏移)
Particle.remainingLifetime  // 剩余生命周期
Particle.startLifetime      // 开始生命周期
Particle.m_RingBufferIndex  // 环形缓冲区索引 (0x1C偏移)
```

## 🎯 目标锁定与路径系统

### 12. 动画器目标匹配 (UnityEngine.Animator)
**位置**: 第113145行
**关键方法**:
```csharp
Animator.MatchTarget()      // 匹配目标 - 用于攻击目标锁定
```

### 13. 路径查找系统 (PathFinding.TriangleNavMesh.TriangleMeshPathFinding)
**位置**: 第22459行
**关键字段**:
```csharp
TriangleMeshPathFinding.pathfinder  // 路径查找器 - 影响攻击路径
```

## 🎪 技能效果系统

### 14. 技能效果助手 (SkillEffectsHelper)
**位置**: 第8105行
**关键方法**:
```csharp
SkillEffectsHelper.UpdateEffectDirection()     // 更新效果方向
SkillEffectsHelper.UpdateHitEffectRotation()   // 更新命中效果旋转
SkillEffectsHelper.IsRotationWithAttackDirection() // 是否与攻击方向旋转
SkillEffectsHelper.UpdateRangeScale()          // 更新范围缩放
SkillEffectsHelper.Disappear()                 // 消失效果
```

### 15. 效果自动销毁 (EffectAutoDestroy)
**位置**: 第6646行
**关键字段**:
```csharp
EffectAutoDestroy.OnEffectPlayingOver  // 效果播放结束回调
EffectAutoDestroy._particleSystems     // 粒子系统数组 (0x18偏移)
```

## 💥 爆发和强度系统

### 16. 发射功率控制
**关键字段**:
```csharp
EmissionPower                   // 发射功率 (0x50偏移)
EmissionColorHDR               // 发射颜色HDR (0x4C偏移)
EmissionPowerID                // 发射功率ID (0x24偏移)
EmissionColorID                // 发射颜色ID (0x20偏移)
```

### 17. 彩票效果控制
**类名**: `LotteryEffectVisibleControl`
**位置**: 第7701行
**关键字段**:
```csharp
LotteryEffectVisibleControl._effectGroupUsed  // 使用的效果组 (0x18偏移)
LotteryEffectVisibleControl.effectGroup1-10   // 效果组1-10 (0x20-0x68偏移)
```

## 🔧 缓冲区系统

### 18. 圆形缓冲区 (CircularBuffer)
**位置**: 第7100行
**关键字段**:
```csharp
CircularBuffer.buffer          // 缓冲区数据 (0x18偏移)
CircularBuffer.head            // 缓冲区头部 (0x14偏移)
CircularBuffer.lockObj         // 锁对象 (0x20偏移)
```

### 19. 各种专用缓冲区
**关键字段**:
```csharp
samplesBuffer                  // 音频样本缓冲区 (0xC0偏移)
vertexBuffer                   // 顶点缓冲区
positionsBuffer                // 位置缓冲区 (0x50偏移)
pointBuffer                    // 点缓冲区 (0x18偏移)
gridsBuffer                    // 网格缓冲区 (0x48偏移)
colorBuffer                    // 颜色缓冲区 (0x90偏移)
renderBuffer                   // 渲染缓冲区 (0x40偏移)
```

## 🎯 后处理效果系统

### 20. 后处理效果强度参数
**位置**: 第98863行起
**关键字段**:
```csharp
directLightingStrength         // 直接照明强度 (0x70偏移)
toneCurveToeStrength          // 色调曲线脚趾强度 (0x40偏移)
toneCurveShoulderStrength     // 色调曲线肩部强度 (0x50偏移)
colorBlindnessStrength        // 色盲强度 (0x20偏移)
```

## 📊 内存修改实战指南

### 攻击类型伤害倍率修改:
```
搜索: 2 (近战攻击原值) → 修改为: 3 (+50%伤害)
搜索: 3 (刺客攻击原值) → 修改为: 5 (+67%伤害)  
搜索: 5 (远程攻击原值) → 修改为: 7 (+40%伤害)
```

### 物理参数优化:
```
DynamicBone.m_Stiffness: 0.1-2.0 (防御力)
DynamicBone.m_Radius: 0.1-5.0 (攻击范围)
DynamicBone.m_Damping: 0.0-1.0 (伤害衰减)
```

### 音频触发伤害加成:
```
AudioVisualiser.scaleOfGreater: 1.0-3.0 (高音伤害加成)
AudioVisualiser.threshold: 0.1-0.9 (触发敏感度)
```

### 效果强度控制:
```
dissolution_strength: 0.0-2.0 (溶解强度)
noise_force: 0.0-5.0 (噪声力度)
EmissionPower: 0.0-10.0 (发射功率)
```

### 粒子生命周期延长:
```
Particle.startLifetime: 1.0-10.0 (粒子持续时间)
Particle.m_Lifetime: 1.0-10.0 (当前生命值)
```

## ⚠️ 安全使用建议

1. **备份存档**: 修改前务必备份游戏存档
2. **渐进测试**: 建议先进行小幅修改测试效果
3. **版本兼容**: 不同游戏版本内存地址可能不同
4. **性能监控**: 过度修改可能影响游戏性能
5. **反作弊**: 注意游戏可能的反作弊保护机制

---
*本分析报告基于IL2CPP反编译文件，适用于Cheat Engine等内存修改工具*
