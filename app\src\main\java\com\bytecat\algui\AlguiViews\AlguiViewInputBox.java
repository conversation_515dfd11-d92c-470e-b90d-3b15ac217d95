package com.bytecat.algui.AlguiViews;
import androidx.annotation.Nullable;
import android.content.ClipData;
import android.content.ClipDescription;
import android.content.ClipboardManager;
import android.content.Context;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.InputType;
import android.text.Selection;
import android.text.TextWatcher;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import com.bytecat.algui.AlguiManager.AlguiCallback;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/12 19:59
 * @Describe 输入框
 */
public class AlguiViewInputBox extends AlguiLinearLayout {

    public static final String TAG = "AlguiViewInputBox";
    Context aContext;
    AlguiViewImage icon;//图标
    EditText editext;//输入框
    AlguiViewButton button;//按钮
    String __Text__="";//输入的文本
    float textSize=-1;//文本大小
    
    
    // 获取图标（AlguiImage）
    public AlguiViewImage getByteIcon() {
        return icon;
    }

    // 获取输入框（EditText）
    public EditText getByteEditText() {
        return editext;
    }

    // 获取按钮（AlguiButton）
    public AlguiViewButton getByteButton() {
        return button;
    }

    // 获取输入的文本 (String)
    public String getByteInputText() {
        return __Text__;
    }

    //输入事件回调反馈
    AlguiCallback.Input call;
    boolean isInitInput = false;//是否已经初始化输入事件
    //初始化内部输入事件
    private void initInput() {
        //输入框输入文本监听器
        editext.addTextChangedListener
        (
            new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int start, int count, int after) {
                    // 在文本变化之前执行的操作
                    
                    if (charSequence != null) {
                        __Text__ = charSequence.toString();
                        if (__Text__ == null)
                            __Text__ = "";
                    } else {
                        __Text__ = "";
                    }

                    if (call != null)
                        call.start(__Text__);


                }
                @Override
                public void onTextChanged(CharSequence charSequence, int start, int before, int count) {
                    // 在文本变化时执行的操作
                    
                    if (charSequence != null) {
                        __Text__ = charSequence.toString();
                        if (__Text__ == null)
                            __Text__ = "";
                    } else {
                        __Text__ = "";
                    }
                 
                    if (call != null) 
                        call.update(__Text__);

                }
                @Override
                public void afterTextChanged(Editable editable) {
                    // 在文本变化之后执行的操作              
                    
                    if (editable != null) {
                        __Text__ = editable.toString();
                        if (__Text__ == null)
                            __Text__ = "";
                    } else {
                        __Text__ = "";
                    }
                  
                    if (call != null) {
                        call.end(__Text__);
                    }
                }
            }
        );
        //按钮点击事件
        button.setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    if (call != null)
                        call.buttonClick(__Text__);
                }
            }
        );

        isInitInput = true;
    }

    //设置点击事件回调反馈接口
    public AlguiViewInputBox setCatCallback(AlguiCallback.Input c) {
        if (c == null) {
            editext.addTextChangedListener(null);
            button.setCatCallback(null);
            isInitInput = false;
        } else {
            call = c;
            if (!isInitInput) {
                initInput();
            }
        }
        return this;
    }

    //获取点击事件回调反馈接口
    public AlguiCallback.Input getByteCallback() {
        return call;
    }
    //代码执行按钮点击事件
    public AlguiViewInputBox callButtonClick() {
        if (call != null)
            call.buttonClick(__Text__);
        return this;
    }



    //&继承父类方法链
    // 设置大小
    public AlguiViewInputBox setCatSize(float w, float h) {
        super.setCatSize(w, h); // 调用父类的方法
        return this;
    }

    // 设置权重
    public AlguiViewInputBox setCatWeight(float weight) {
        super.setCatWeight(weight); // 调用父类的方法
        return this;
    }

    // 设置内边距
    public AlguiViewInputBox setCatPadding(float left, float top, float right, float bottom) {
        super.setCatPadding(left, top, right, bottom); // 调用父类的方法
        return this;
    }

    // 设置外边距
    public AlguiViewInputBox setCatMargins(float left, float top, float right, float bottom) {
        super.setCatMargins(left, top, right, bottom); // 调用父类的方法
        return this;
    }

    // 设置背景颜色
    public AlguiViewInputBox setCatBackColor(int... backColor) {
        super.setCatBackColor(backColor); // 调用父类的方法
        return this;
    }

    // 设置圆角半径
    public AlguiViewInputBox setCatRadiu(float radiu) {
        super.setCatRadiu(radiu); // 调用父类的方法
        return this;
    }

    // 设置描边
    public AlguiViewInputBox setCatBorder(float borderSize, int borderColor) {
        super.setCatBorder(borderSize, borderColor); // 调用父类的方法
        return this;
    }

    // 设置父布局
    public AlguiViewInputBox setCatParentLayout(ViewGroup vg) {
        super.setCatParentLayout(vg); // 调用父类的方法
        return this;
    }





    //设置所有文本大小
    public AlguiViewInputBox setCatTextSize(float size) {
        if (editext != null)
            editext.setTextSize(TypedValue.COMPLEX_UNIT_PX, dp2px(size));//输入框文本大小
        if (button != null)
            button.setCatTextSize(size);//按钮文本大小
        if (icon != null)
            icon.setCatSize(size, size);//图标大小
        textSize = size;
        return this;
    }








    //设置图标
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiViewInputBox setCatIcon(@Nullable String Url_Base64_FilePath) {
        if (Url_Base64_FilePath != null) {
            if (icon == null) {
                icon = new AlguiViewImage(aContext);
                icon.setCatMargins(4, 0, 0, 0);
                if (textSize != -1)
                    icon.setCatSize(textSize, textSize);
            }
            icon.setCatImage(Url_Base64_FilePath);

            if (indexOfChild(icon) < 0)
                addView(icon, 0);
        } else {
            //删除图标
            if (icon != null)
                if (indexOfChild(icon) > -1) {
                    remView(icon);
                }
        }
        return this;
    }

    //设置图标颜色 (只支持单色图标)
    public AlguiViewInputBox setCatIconColor(int color) {
        if (icon != null)
            icon.setCatColor(color);
        return this;
    }

    //设置图标圆角半径
    public AlguiViewInputBox setCatIconRadiu(float r) {
        if (icon != null)
            icon.setCatRadiu(r); 
        return this;
    }


    //设置图标透明度
    public AlguiViewInputBox setCatIconTransparent(int t) {
        if (icon != null)
            icon.setCatTransparent(t);
        return this;
    }

    //设置图标毛玻璃模糊 (不支持GIF动态图片模糊)
    public AlguiViewInputBox setCatIconBlur(int radius) {
        if (icon != null)
            icon.setCatBlur(radius);
        return this;
    }





//输入类型：
// TYPE_CLASS_TEXT - 用于普通文本输入。适用于大多数文本输入框，显示常规的字符输入键盘。
// TYPE_TEXT_VARIATION_PASSWORD - 用于密码输入。输入内容会以星号（或其他符号）隐藏，适用于密码输入框。
// TYPE_TEXT_VARIATION_VISIBLE_PASSWORD - 用于可见密码输入。输入的内容以明文显示，适用于用户希望查看密码的场景。
// TYPE_TEXT_VARIATION_WEB_PASSWORD - 用于网页密码输入。常用于网页表单中的密码字段，通常有特定的密码管理优化。
// TYPE_TEXT_VARIATION_URI - 用于URI输入。适用于输入网址或URI（如：http://或ftp://）。
// TYPE_TEXT_VARIATION_EMAIL_ADDRESS - 用于电子邮件地址输入。键盘会优化为适合输入电子邮件地址，如自动补充 `@` 符号。
// TYPE_TEXT_VARIATION_PERSON_NAME - 用于输入人名。通常显示优化的人名键盘，便于用户输入全名、姓氏等。
// TYPE_TEXT_VARIATION_LONG_MESSAGE - 用于输入较长的文本消息。通常会启用多行输入框，并显示“换行”键。
// TYPE_CLASS_NUMBER - 用于数字输入。显示数字键盘，适合输入价格、数量等数字信息。
// TYPE_NUMBER_VARIATION_NORMAL - 用于常规数字输入。适合输入数字，没有密码或特殊格式要求。
// TYPE_NUMBER_VARIATION_PASSWORD - 用于数字密码输入。适用于需要输入数字密码的场景，输入内容会隐藏。
// TYPE_CLASS_PHONE - 用于电话号码输入。显示专用的电话号码键盘，优化了电话号码输入的格式。
// TYPE_CLASS_DATETIME - 用于日期或时间输入。显示适合选择日期或时间的键盘。
// TYPE_TEXT_FLAG_NO_SUGGESTIONS - 禁用文本建议。输入框不会显示任何自动补全或预测文本建议。
// TYPE_TEXT_FLAG_AUTO_COMPLETE - 启用自动完成功能。通常用于输入电子邮件地址、URL、用户名等，系统会提供自动完成的建议。
// TYPE_TEXT_FLAG_CAP_CHARACTERS - 强制所有输入字符为大写。输入的每个字母都会自动转为大写。
// TYPE_TEXT_FLAG_CAP_SENTENCES - 自动将每个句子的首字母转换为大写。适用于正常文本输入。
// TYPE_TEXT_FLAG_CAP_WORDS - 自动将每个单词的首字母转换为大写。常用于标题或专有名词的输入。
// TYPE_TEXT_FLAG_MULTI_LINE - 启用多行输入。允许输入换行符，适合需要输入长文本（如评论、消息等）的场景。
    //设置输入类型
    public AlguiViewInputBox setCatInputType(int type) {
        editext.setInputType(type);
        return this;
    }
    //设置输入框提示
    public AlguiViewInputBox setCatInputHint(CharSequence text) {
        if (text == null)
            text = "";
        editext.setHint(text);
        return this;
    }
    //设置输入框提示颜色
    public AlguiViewInputBox setCatInputHintColor(int color) {
        editext.setHintTextColor(color);
        return this;
    }
    //设置输入框输入内容 (会触发输入事件)
    public AlguiViewInputBox setCatInputText(CharSequence text) {
        if (text == null)
            text = "";
        editext.setText(text);
        __Text__=text.toString();
        return this;
    }
    //设置输入框输入内容颜色
    public AlguiViewInputBox setCatInputTextColor(int color) {
        editext.setTextColor(color);
        return this;
    }











    //设置按钮背景颜色
    public AlguiViewInputBox setCatButtonBackColor(int... backColor) {
        button.setCatBackColor(backColor);
        return this;
    }
    //设置按钮圆角半径
    public AlguiViewInputBox setCatButtonRadiu(float r) {
        button.setCatRadiu(r);
        return this;
    }
    //设置按钮描边
    public AlguiViewInputBox setCatButtonBorder(float size, int color) {
        button.setCatBorder(size, color);
        return this;
    }
    //设置按钮文本
    public AlguiViewInputBox setCatButtonText(CharSequence text) {
        if(text!=null){
            button.setVisibility(LinearLayout.VISIBLE);
            button.setCatText(text);
        }else{
            button.setVisibility(LinearLayout.GONE);
        }
        return this;
    }
    //设置按钮文本颜色
    public AlguiViewInputBox setCatButtonTextColor(int... color) {
        button.setCatTextColor(color);
        return this;
    }
    //设置按钮文本动态渐变效果启动状态
    public AlguiViewInputBox setCatButtonTextMoveGrad(boolean b) {
        button.setCatTextMoveGrad(b);
        return this;
    }
    //设置按钮文本发光效果
    public AlguiViewInputBox setCatButtonTextGlow(float radius, int color) {
        button.setCatTextGlow(radius, color);
        return this;
    }






    public AlguiViewInputBox(Context context) {
        super(context);
        aContext = context;
        init();
    }
    private void init() {
        super.setCatSize(LinearLayout.LayoutParams.MATCH_PARENT,
                         LinearLayout.LayoutParams.WRAP_CONTENT);
        super.setCatWeight(1);
        super.setCatBackColor(0xff20324D);
        super.setOrientation(LinearLayout.HORIZONTAL);//横向
        super.setGravity(Gravity.CENTER_VERTICAL);//垂直居中


        //输入框
        LinearLayout.LayoutParams editLayoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                                                                                   LinearLayout.LayoutParams.WRAP_CONTENT, 1);

        editext = new EditText(aContext);
        editext.setTextIsSelectable(true);//启用输入框文本选择功能
        //设置内容过多时光标拖动时查看之前输入的内容是横向的
        //editext.setInputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE); //设置内容只有单行
        //editext.setInputType(InputType.TYPE_NUMBER_VARIATION_PASSWORD); // 按钮点击事件
       // editext.setInputType(InputType.TYPE_TEXT_VARIATION_PERSON_NAME); 
        editext.setLayoutParams(editLayoutParams);

        editext.setHint(TAG);//设置输入框提示
        editext.setHintTextColor(0xFFFFFFFF);//提示颜色
        editext.setTextColor(0xC200FF00);//输入内容颜色

        int padding =(int)dp2px(3);
        editext.setPadding(padding, padding, padding, padding);//覆盖输入框原来的内边距
        editext.setBackground(null);//设置一个空背景来覆盖默认输入框的线条

        button = new AlguiViewButton(aContext, "确定");
        button.setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.MATCH_PARENT);

        setCatTextSize(7);//设置文本大小

        addView(editext);
        addView(button);

        // 设置长按监听器
        editext.setOnLongClickListener(new View.OnLongClickListener() {

                AlguiFlowLayout layout;
                AlguiViewButton selectAll;
                AlguiViewButton copy;
                AlguiViewButton paste;
                AlguiViewButton shearing;
                PopupWindow popupWindow;
                boolean isInit=false;
                //检查是否正在全选
                private boolean isTextSelectedAll() {
                    int start = Selection.getSelectionStart(editext.getText());
                    int end = Selection.getSelectionEnd(editext.getText());

                    // 判断是否全选
                    return start == 0 && end == editext.getText().length();
                }
                @Override
                public boolean onLongClick(View v) {

                    if (!isInit) {
                        //流式布局
                        layout = new AlguiFlowLayout(aContext);
                        layout.setCatBackColor(0xff505050);//设置布局背景色 让按钮缩放动画后显示此颜色营造按钮按下后描边效果

                        selectAll =  new AlguiViewButton(aContext, "全选")
                            .setCatBackColor(0xff2C2C2E)
                            .setCatParentLayout(layout)
                            .setCatCallback(new AlguiCallback.Click(){
                                public void click(boolean b) {
                                    editext.selectAll();
                                }
                            }
                        );
                        copy = new AlguiViewButton(aContext, "复制")
                            .setCatBackColor(0xff2C2C2E)
                            .setCatParentLayout(layout)
                            .setCatCallback(new AlguiCallback.Click(){
                                public void click(boolean b) {
                                    ClipboardManager clipboardManager = (ClipboardManager) aContext.getSystemService(Context.CLIPBOARD_SERVICE);
                                    ClipData clipData = ClipData.newPlainText("text", editext.getText());
                                    clipboardManager.setPrimaryClip(clipData);
                                }
                            }
                        );
                        paste =  new AlguiViewButton(aContext, "粘贴")
                            .setCatBackColor(0xff2C2C2E)
                            .setCatParentLayout(layout)
                            .setCatCallback(new AlguiCallback.Click(){
                                public void click(boolean b) {
                                    ClipboardManager clipboardManager = (ClipboardManager) aContext.getSystemService(Context.CLIPBOARD_SERVICE);
                                    if (clipboardManager.hasPrimaryClip() && clipboardManager.getPrimaryClipDescription().hasMimeType(ClipDescription.MIMETYPE_TEXT_PLAIN)) {
                                        //如果是全选则清除所有文本
                                        if (isTextSelectedAll())
                                            editext.setText("");
                                        ClipData.Item cItem = clipboardManager.getPrimaryClip().getItemAt(0);
                                        String pasteText = cItem.getText().toString();
                                        //将粘贴的文本插入到输入框中
                                        editext.getText().insert(editext.getSelectionStart(), pasteText);
                                    }
                                }
                            }
                        );
                        shearing = new AlguiViewButton(aContext, "剪切")
                            .setCatBackColor(0xff2C2C2E)
                            .setCatParentLayout(layout)
                            .setCatCallback(new AlguiCallback.Click(){
                                public void click(boolean b) {
                                    ClipboardManager clipboardManager = (ClipboardManager) aContext.getSystemService(Context.CLIPBOARD_SERVICE);
                                    ClipData clipData = ClipData.newPlainText("text", editext.getText());
                                    clipboardManager.setPrimaryClip(clipData);//先复制文本
                                    editext.setText(""); //清除输入框文本
                                }
                            }
                        );
                        //创建弹窗
                        popupWindow = new PopupWindow(layout, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                        //popupWindow.setFocusable(true);//设置焦点
                        popupWindow.setOutsideTouchable(true);//设置点击外部是否销毁
                        isInit = true;
                    }
                    //让按钮跟随输入框大小
                    float size = textSize;
                    if (size < 0)
                        size = 7;
                    selectAll.setCatTextSize(size);
                    copy.setCatTextSize(size);
                    paste.setCatTextSize(size);
                    shearing.setCatTextSize(size);

                    popupWindow.showAsDropDown(v, 0, 0, Gravity.LEFT);//显示弹窗

                    return false;//不要返回true否则影响输入框长按选择文本
                }
            });






    }



}
