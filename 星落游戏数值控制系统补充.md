# 🔢 星落游戏数值控制系统补充分析

## 📖 说明
本文档是对主要伤害系统分析的补充，专门收集与数值控制、缩放、计数器相关的类名和字段。

## 🎯 核心数值控制系统

### 1. 更新率控制系统
**关键字段**:
```csharp
m_UpdateRate                   // 主更新率 (0x28偏移)
updateRate                     // 陀螺仪更新率 (0x38偏移)  
m_UpdateCount                  // 更新计数器 (0x2C偏移)
m_PreUpdateCount              // 预更新计数 (0x110偏移)
s_UpdateCount                 // 静态更新计数 (0x2C偏移)
```

### 2. 缩放倍率系统
**关键字段**:
```csharp
m_ObjectScale                  // 对象整体缩放 (0x100偏移)
m_ScaledRadius                 // 缩放后半径 (0x3C偏移)
m_ScaledRadius2                // 缩放后半径平方 (0x40偏移)
sampleScale                    // 音频样本缩放 (0x48偏移)
outerScalefactor              // 外部效果缩放因子 (0x50偏移)
UIAdaptScale                   // UI适配缩放 (0xC0偏移)
scale_                         // 通用缩放值 (0xC8偏移)
```

### 3. 计数器控制系统
**关键字段**:
```csharp
maxCount                       // 最大效果计数 (0x20偏移)
m_ChildCount                   // 子对象计数 (0x1C偏移)
s_RemainWorkCount             // 剩余工作计数 (0x18偏移)
generatedWaveDistancesCount   // 生成波距计数 (0x10偏移)
m_RenderFrameRate             // 渲染帧率 (0x30偏移)
```

### 4. 阈值响应系统
**关键字段**:
```csharp
threshold                      // 触发阈值 (0x5C偏移)
scaleOfLess                   // 低阈值缩放倍率 (0x60偏移)
scaleOfGreater                // 高阈值缩放倍率 (0x64偏移) - 重要伤害加成
```

### 5. 音频数值控制
**关键字段**:
```csharp
frequencyRate                  // 音频频率率 (0x28偏移)
waveNoiseScale                // 波形噪声缩放 (0x2C偏移)
waveNoiseInfluence            // 波形噪声影响度 (0x30偏移)
sampleElapse                  // 样本流逝时间 (0x44偏移)
smoothQuality                 // 平滑质量 (0x4C偏移)
```

## 🎮 变换控制系统

### 6. 位置缩放控制
**类名**: `TransformExtension`
**关键方法**:
```csharp
SetScaleX()                   // 设置X轴缩放
SetScaleY()                   // 设置Y轴缩放  
SetScaleZ()                   // 设置Z轴缩放
SetScaleXY()                  // 设置XY轴缩放
SetScaleXYZ()                 // 设置XYZ轴缩放
ScaleX()                      // X轴缩放变换
ScaleY()                      // Y轴缩放变换
ScaleZ()                      // Z轴缩放变换
```

### 7. 陀螺仪数值控制
**类名**: `InputGyroscope`
**关键字段**:
```csharp
updateRate                    // 陀螺仪更新率 (0x38偏移)
m_CurGyroX                    // 当前陀螺仪X值 (0x3C偏移)
```

**关键方法**:
```csharp
AddGyroGameObject(rangeX, rangeY, speedX, speedY, lerpValue)  // 添加陀螺仪控制对象
```

## 🔧 键值对数值系统

### 8. 序列化字典系统
**类名**: `SerializableDictionary<TKey,TValue>`
**关键字段**:
```csharp
Value                         // 键值对中的值
Key                           // 键值对中的键
```

**关键方法**:
```csharp
get_Value()                   // 获取值
set_Item()                    // 设置项目值
TryGetValue()                 // 尝试获取值
get_Count()                   // 获取计数
```

## 🎵 音频生成数值控制

### 9. 音频生成系统
**类名**: `LeanAudio`
**关键字段**:
```csharp
PROCESSING_ITERATIONS_MAX     // 最大处理迭代次数
generatedWaveDistances        // 生成的波距数组
generatedWaveDistancesCount   // 波距计数
```

**关键方法**:
```csharp
generateAudioFromCurve()      // 从曲线生成音频
printOutAudioClip()           // 打印音频剪辑数据
```

### 10. 音频选项控制
**类名**: `LeanAudioOptions`
**关键字段**:
```csharp
frequencyRate                 // 频率率 (0x28偏移)
waveNoiseScale               // 波噪声缩放 (0x2C偏移)
waveNoiseInfluence           // 波噪声影响 (0x30偏移)
modulation                   // 调制数组 (0x20偏移)
```

## 📊 内存修改数值策略

### 更新率优化:
```
m_UpdateRate: 30-120 (影响游戏响应速度，建议60-90)
updateRate: 30-60 (陀螺仪更新，建议45-60)
m_UpdateCount: 监控值，不建议直接修改
```

### 缩放倍率调整:
```
m_ObjectScale: 0.5-3.0 (对象整体缩放，影响碰撞检测)
outerScalefactor: 0.1-5.0 (外部效果缩放，影响视觉效果)
UIAdaptScale: 0.8-2.0 (UI缩放，影响界面显示)
sampleScale: 0.5-2.0 (音频样本缩放，影响音效强度)
```

### 阈值敏感度调整:
```
threshold: 0.1-0.9 (触发敏感度，越小越敏感)
scaleOfLess: 0.5-1.5 (低阈值响应倍率)
scaleOfGreater: 1.0-5.0 (高阈值响应倍率，直接影响伤害输出)
```

### 音频效果增强:
```
frequencyRate: 22050-48000 (音频采样率，影响音质)
waveNoiseScale: 0.0-2.0 (波形噪声强度，影响音效变化)
waveNoiseInfluence: 0.0-1.0 (噪声影响程度，影响音效随机性)
```

### 计数器控制:
```
maxCount: 10-1000 (最大效果数量，影响同时显示的效果数)
m_ChildCount: 只读值，显示子对象数量
generatedWaveDistancesCount: 波形生成相关，影响音频效果复杂度
```

## 🎯 Cheat Engine 搜索指南

### 1. 基础数值搜索:
- **搜索类型**: 单精度浮点数 (Float)
- **初始值**: 1.0 (很多缩放值的默认值)
- **变化测试**: 在游戏中触发相关效果观察数值变化

### 2. 阈值系统搜索:
- **搜索范围**: 0.0-1.0 之间的浮点数
- **关键值**: 0.5 (很多阈值的默认值)
- **验证方法**: 修改后观察音频或视觉效果的触发变化

### 3. 计数器搜索:
- **搜索类型**: 4字节整数
- **常见值**: 30, 60, 120 (更新率相关)
- **验证方法**: 观察游戏流畅度变化

### 4. 缩放系统搜索:
- **搜索类型**: 单精度浮点数
- **常见值**: 1.0, 0.5, 2.0
- **验证方法**: 观察对象大小或效果强度变化

## ⚠️ 修改注意事项

1. **渐进调整**: 建议小幅度调整数值，观察效果后再进一步修改
2. **备份数值**: 记录原始数值，以便恢复
3. **性能监控**: 过高的更新率可能影响游戏性能
4. **兼容性**: 某些数值修改可能影响其他系统的正常运行
5. **测试环境**: 建议在非重要存档中测试修改效果

## 🔍 高级搜索技巧

### 组合搜索:
1. 先搜索已知的基础值（如1.0的缩放值）
2. 在附近内存区域搜索相关的阈值和倍率
3. 通过修改测试确认各字段的功能关联

### 动态追踪:
1. 使用Cheat Engine的"查找访问此地址的代码"功能
2. 监控数值在游戏运行时的变化模式
3. 根据变化时机确定数值的具体作用

### 批量修改:
1. 确定一组相关的数值字段
2. 按比例同时调整多个相关参数
3. 测试整体效果的协调性

---
*本补充分析基于IL2CPP反编译文件，专注于数值控制系统的内存修改应用*
