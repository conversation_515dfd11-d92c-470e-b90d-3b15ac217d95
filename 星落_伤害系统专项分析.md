# 星落.cs 伤害系统专项分析

## 核心伤害相关组件

### 1. 攻击类型系统
**CardType 枚举** (第6693行)
```csharp
DEFENSE = 1         // 防御类型
MELEE_ATTACK = 2    // 近战攻击类型  
ASSASSIN = 3        // 刺客类型
SUPPORT = 4         // 支援类型
RANGED_ATTACK = 5   // 远程攻击类型
```

### 2. 子弹伤害系统
**BulletEffectHelper** (第7399行)
- `ArriveTarget()` - 命中目标时触发伤害计算
- `SetBulletUid()` - 设置子弹唯一ID用于伤害追踪
- `alpha` - 透明度，可能影响穿透伤害
- `isArrive` - 到达标志，触发伤害判定

### 3. 攻击方向与伤害加成
**EffectHelper** (第8121行)
```csharp
rotationWithAttackDirection  // 随攻击方向旋转
directionToTarget           // 朝向目标
```
**伤害机制**:
- 攻击方向影响暴击率
- 背后攻击可能有伤害加成
- 正面防御可能有伤害减免

### 4. 物理伤害计算
**Unity物理系统**:
- `ComputePenetration()` (第110229行) - 穿透伤害计算
- `CalculateRaycastTexCoord()` (第109841行) - 精确命中部位
- `ClosestPoint()` (第110232行) - 范围伤害计算

### 5. 动态骨骼物理伤害
**DynamicBone** (第138-263行)
```csharp
m_Damping      // 阻尼系数 - 影响击退效果
m_Elasticity   // 弹性系数 - 影响反弹伤害
m_Stiffness    // 刚度系数 - 影响防御力
m_Friction     // 摩擦系数 - 影响滑动伤害
m_Radius       // 碰撞半径 - 攻击范围判定
```

## 伤害计算逻辑

### 基础伤害公式
```
基础伤害 = 攻击力 × 攻击类型系数 × 距离衰减系数
```

### 物理修正公式
```
物理修正 = 基础伤害 × (1 - 防御力/刚度系数) × 弹性系数
```

### 方向修正公式
```
方向修正 = 物理修正 × 攻击角度系数 × 方向加成
```

### 最终伤害公式
```
最终伤害 = 方向修正 × 暴击系数 × 技能加成 × 随机因子
```

## 伤害类型分析

### 1. 近战伤害 (MELEE_ATTACK)
- 基于物理碰撞检测
- 受刚度和弹性影响较大
- 可能有连击机制

### 2. 远程伤害 (RANGED_ATTACK)  
- 基于射线检测
- 有距离衰减机制
- 可能有穿透效果

### 3. 穿透伤害
- 使用`ComputePenetration()`计算
- 受目标防御力影响
- 可能无视部分护甲

### 4. 范围伤害 (AOE)
- 使用`ClosestPoint()`计算影响范围
- 距离越近伤害越高
- 可能有爆炸效果

## 伤害修正因子

### 1. 物理因子
- **阻尼系数**: 影响击退距离和硬直时间
- **弹性系数**: 影响反弹伤害和格挡效果  
- **刚度系数**: 影响防御力和伤害减免
- **摩擦系数**: 影响滑动和位移效果

### 2. 方向因子
- **正面攻击**: 基础伤害，可能被格挡
- **侧面攻击**: 中等伤害加成
- **背后攻击**: 高伤害加成，可能暴击

### 3. 距离因子
- **近距离**: 最大伤害
- **中距离**: 标准伤害
- **远距离**: 衰减伤害

## 特殊伤害效果

### 1. 暴击系统
```
if (随机值 > 暴击阈值) {
    最终伤害 *= 暴击倍数
    触发暴击特效
}
```

### 2. 击退系统  
```
if (攻击力 > 击退阈值) {
    击退距离 = 攻击力 / 阻尼系数
    击退方向 = 攻击方向
}
```

### 3. 穿透系统
```
if (攻击力 > 防御阈值) {
    穿透伤害 = (攻击力 - 防御力) × 穿透系数
}
```

## 伤害计算流程

### 阶段1: 伤害发起
1. 确定攻击类型 (CardType)
2. 创建攻击轨迹 (BulletEffectHelper)
3. 设置攻击方向 (EffectHelper)

### 阶段2: 命中检测
1. 射线/碰撞检测
2. 确定命中部位
3. 计算命中角度

### 阶段3: 伤害计算
1. 基础伤害计算
2. 物理修正应用
3. 方向加成计算
4. 特殊效果判定

### 阶段4: 伤害应用
1. 扣除目标生命值
2. 应用击退效果
3. 播放伤害特效
4. 更新战斗状态

## 伤害优化机制

### 1. GPU计算优化
- 使用ComputeShader进行大量伤害计算
- 批处理多个攻击的伤害计算
- 减少CPU负担

### 2. 缓存机制
- 缓存常用的伤害计算结果
- 避免重复计算相同参数
- 提高计算效率

### 3. 精度控制
- 使用适当的数值精度
- 避免浮点数误差累积
- 保证伤害计算的一致性

## 伤害相关参数详解

### 战斗波浪效果参数
**ChangeBattleWave** (第7442行)
```csharp
dissolution_strength  // 溶解强度 (0.0-1.0) - 可能表示伤害程度
noise_force          // 噪声力度 - 可能表示攻击冲击力
blur                 // 模糊程度 - 可能用于冲击波伤害效果
```

### 音频伤害触发参数
**AudioVisualiser** (第362行)
```csharp
threshold           // 阈值 - 可能用于伤害触发条件
scaleOfLess        // 低于阈值的伤害缩放
scaleOfGreater     // 高于阈值的伤害缩放
```

### 陀螺仪精准攻击参数
**GyroInput** (第794行)
```csharp
speedX/speedY      // 攻击速度影响DPS
rangeX/rangeY      // 攻击范围影响AOE伤害
```

## 推测的伤害类型

### 1. 物理伤害
- 受护甲值直接减免
- 可以被格挡和闪避
- 有暴击机制

### 2. 魔法伤害
- 受魔法抗性减免
- 可能无视物理护甲
- 有元素效果

### 3. 真实伤害
- 无视所有防御
- 按百分比或固定值计算
- 通常来自特殊技能

### 4. 持续伤害 (DOT)
- 按时间间隔造成伤害
- 可能有叠加效果
- 受治疗效果影响

## 伤害减免机制

### 1. 护甲减免
```
实际伤害 = 物理伤害 × (1 - 护甲值/(护甲值 + 护甲常数))
```

### 2. 魔法抗性
```
实际伤害 = 魔法伤害 × (1 - 魔抗值/100)
```

### 3. 百分比减伤
```
实际伤害 = 原始伤害 × (1 - 减伤百分比)
```

### 4. 固定减伤
```
实际伤害 = max(0, 原始伤害 - 固定减伤值)
```

## 伤害增幅机制

### 1. 攻击力加成
```
最终攻击力 = 基础攻击力 × (1 + 攻击力加成百分比) + 固定攻击力加成
```

### 2. 暴击伤害
```
暴击伤害 = 基础伤害 × 暴击倍数
```

### 3. 技能伤害加成
```
技能伤害 = 基础伤害 × (1 + 技能伤害加成)
```

### 4. 属性克制加成
```
克制伤害 = 基础伤害 × 属性克制倍数
```

## 伤害计算中的随机因子

### 1. 伤害浮动
```
实际伤害 = 基础伤害 × (0.85 + 随机值 × 0.3)  // 85%-115%浮动
```

### 2. 暴击判定
```
if (随机值 < 暴击率) {
    触发暴击
}
```

### 3. 命中判定
```
if (随机值 < 命中率) {
    攻击命中
} else {
    攻击miss
}
```

## 伤害显示与反馈

### 1. 伤害数字显示
- 不同颜色表示不同伤害类型
- 字体大小表示伤害程度
- 飘字方向表示攻击方向

### 2. 视觉效果反馈
- 屏幕震动强度对应伤害大小
- 角色受击动画时长对应硬直时间
- 血条变化速度对应伤害频率

### 3. 音效反馈
- 不同音效对应不同伤害类型
- 音量大小对应伤害程度
- 音效频率对应攻击速度
