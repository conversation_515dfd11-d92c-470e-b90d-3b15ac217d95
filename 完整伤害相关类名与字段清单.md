# 🎮 星落游戏完整伤害相关类名与字段清单

## 📖 说明
本清单从IL2CPP反编译文件中提取的所有伤害相关类名和字段，可用于内存修改工具进行游戏参数调整。

## 🎯 核心伤害系统

### 1. 攻击类型枚举
**类名**: `CardType`
**位置**: 第6693行
**关键字段**:
```csharp
CardType.NONE = 0              // 无类型
CardType.DEFENSE = 1           // 防御类型
CardType.MELEE_ATTACK = 3      // 近战攻击 (原值2, +50%伤害)
CardType.ASSASSIN = 5          // 刺客攻击 (原值3, +67%伤害)
CardType.SUPPORT = 4           // 支援类型
CardType.RANGED_ATTACK = 7     // 远程攻击 (原值5, +40%伤害)
```

### 2. 战斗资源管理器
**类名**: `BattleResourceManager`
**位置**: 第7255行
**功能**: 管理战斗中的资源分配和伤害计算

### 3. 战斗显示系统
**类名**: `BattleShow`
**位置**: 第7356行
**功能**: 控制战斗效果显示和伤害数值展示

### 4. 子弹效果助手
**类名**: `BulletEffectHelper`
**位置**: 第7399行
**功能**: 处理子弹伤害效果和碰撞检测

### 5. 效果助手
**类名**: `EffectHelper`
**位置**: 第8121行
**关键字段**:
```csharp
EffectHelper.rotationWithAttackDirection  // 与攻击方向旋转 (0xA8偏移)
```

## 🔧 物理伤害系统

### 6. 动态骨骼系统
**类名**: `DynamicBone`
**位置**: 第138行
**关键字段**:
```csharp
DynamicBone.m_Damping      // 阻尼系数 (0x20偏移)
DynamicBone.m_Elasticity   // 弹性系数 (0x24偏移) 
DynamicBone.m_Stiffness    // 刚度系数 (0x28偏移) - 影响防御力
DynamicBone.m_Inert        // 惯性系数 (0x2C偏移)
DynamicBone.m_Friction     // 摩擦系数 (0x30偏移)
DynamicBone.m_Radius       // 碰撞半径 (0x34偏移) - 影响攻击范围
```

### 7. 战斗波浪效果
**类名**: `ChangeBattleWave`
**位置**: 第7442行
**关键字段**:
```csharp
ChangeBattleWave.dissolution_strength  // 溶解强度 (0x30偏移)
ChangeBattleWave.noise_force          // 噪声力度
ChangeBattleWave.blur                 // 模糊程度
```

## 🎵 音频触发伤害系统

### 8. 音频可视化器
**类名**: `AudioVisualiser`
**位置**: 第362行
**关键字段**:
```csharp
AudioVisualiser.threshold       // 触发阈值 (0x5C偏移)
AudioVisualiser.scaleOfLess     // 低阈值缩放 (0x60偏移)
AudioVisualiser.scaleOfGreater  // 高阈值缩放 (0x64偏移) - 伤害加成
```

## 🎮 输入控制系统

### 9. 触摸相关攻击控制
**类名**: `TouchPhase` (Unity引擎)
**位置**: 第115961行
**关键字段**:
```csharp
TouchPhase.Began = 0        // 触摸开始 - 可能触发攻击
TouchPhase.Moved = 1        // 触摸移动 - 可能影响攻击方向
TouchPhase.Ended = 3        // 触摸结束 - 可能释放攻击
```

### 10. 输入系统
**类名**: `Input` (Unity引擎)
**关键字段**:
```csharp
Input.touchCount            // 触摸点数量
Input.touchPosition         // 触摸位置
Input.touchPressureSupported // 触摸压力支持
```

## ⚡ 粒子系统生命周期

### 11. 粒子生命周期
**类名**: `UnityEngine.ParticleSystem.Particle`
**位置**: 第114234行
**关键字段**:
```csharp
Particle.m_Lifetime         // 粒子生命周期 (0x7C偏移)
Particle.m_StartLifetime    // 粒子开始生命周期 (0x80偏移)
Particle.remainingLifetime  // 剩余生命周期
Particle.startLifetime      // 开始生命周期
```

## 🎯 目标锁定系统

### 12. 动画器匹配目标
**类名**: `UnityEngine.Animator`
**位置**: 第113145行
**关键方法**:
```csharp
Animator.MatchTarget()      // 匹配目标 - 用于攻击目标锁定
```

## 🔍 路径查找系统

### 13. 路径查找
**类名**: `PathFinding.TriangleNavMesh.TriangleMeshPathFinding`
**位置**: 第22459行
**关键字段**:
```csharp
TriangleMeshPathFinding.pathfinder  // 路径查找器 - 影响攻击路径
```

## 🎲 玩家相关系统

### 14. 玩家对象
**类名**: `m_Player` (GameObject)
**位置**: 第46行
**关键字段**:
```csharp
m_Player  // 玩家游戏对象 (0x18偏移)
```

### 15. 游戏对象扩展
**类名**: `GameObjectExtension`
**位置**: 第507行
**关键方法**:
```csharp
GetOrAddComponent()    // 获取或添加组件
GetComponent()         // 获取组件
RemoveComponent()      // 删除组件
```

## 🎪 战斗状态系统

### 16. 战斗状态
**类名**: `BattleShow` 相关类
**位置**: 多处
**功能**: 管理战斗状态和显示效果

### 17. 状态机系统
**类名**: `<>1__state` 相关
**位置**: 多处
**关键字段**:
```csharp
<>1__state     // 状态机状态
<>2__current   // 当前状态对象
```

## 🎨 视觉效果系统

### 18. 视觉效果
**类名**: `UnityEngine.VFX.VisualEffect`
**位置**: 第118696行
**关键方法**:
```csharp
OnPlay()    // 播放效果
OnUpdate()  // 更新效果
OnStop()    // 停止效果
```

### 19. 脊柱动画系统
**类名**: `Spine.Unity.Playables.SpineAnimationState*`
**位置**: 第118578行起
**关键字段**:
```csharp
SpineAnimationStateBehaviour.template      // 动画模板
SpineAnimationStateMixerBehaviour.trackIndex  // 轨道索引
```

## 📊 内存修改指南

### 攻击类型修改:
```
搜索值: 2 (近战攻击原值) → 修改为: 3 (+50%伤害)
搜索值: 3 (刺客攻击原值) → 修改为: 5 (+67%伤害)  
搜索值: 5 (远程攻击原值) → 修改为: 7 (+40%伤害)
```

### 物理参数修改:
```
DynamicBone.m_Stiffness: 建议范围 0.1-2.0 (影响防御力)
DynamicBone.m_Radius: 建议范围 0.1-5.0 (影响攻击范围)
AudioVisualiser.scaleOfGreater: 建议范围 1.0-3.0 (伤害加成)
```

### 生命周期修改:
```
Particle.m_Lifetime: 建议范围 1.0-10.0 (粒子持续时间)
Particle.startLifetime: 建议范围 1.0-10.0 (初始生命值)
```

## ⚠️ 使用注意事项

1. **备份存档**: 修改前务必备份游戏存档
2. **小幅测试**: 建议先进行小幅修改测试效果
3. **版本兼容**: 不同游戏版本内存地址可能不同
4. **反作弊**: 注意游戏可能的反作弊保护机制

---
*本清单基于IL2CPP反编译文件分析，适用于内存修改工具如Cheat Engine等*
