package com.bytecat.algui.AlguiTools;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import com.bytecat.algui.AlguiManager.AlguiLog;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/24 17:07
 * @Describe 布局加载工具类 - 从assets目录动态加载布局
 */
public class AlguiToolLayout {

    public static final String TAG = "AlguiToolLayout";

    private AlguiToolLayout() {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  

    /**
     * 从assets目录加载布局文件
     * @param context 上下文
     * @param layoutName 布局名称（如：activity_main）
     * @return View对象，加载失败时返回null
     */
    public static View loadLayoutFromAssets(Context context, String layoutName) {
        if (context == null || layoutName == null || layoutName.isEmpty()) {
            return null;
        }

        try {
            // 获取布局文件名
            String fileName = AlguiToolResource.getLayoutFileName(layoutName);
            
            // 从assets加载布局XML内容
            String xmlContent = AlguiToolResource.loadTextFromAssets(context, fileName);
            if (xmlContent != null) {
                // 使用LayoutInflater解析XML内容
                LayoutInflater inflater = LayoutInflater.from(context);
                // 注意：这里需要特殊处理，因为LayoutInflater通常不能直接从字符串解析
                // 我们可能需要使用其他方法
                AlguiLog.d(TAG, "布局文件内容加载成功: " + fileName);
                return inflater.inflate(getLayoutResourceId(context, layoutName), null);
            } else {
                AlguiLog.w(TAG, "布局文件不存在: " + fileName);
                return null;
            }
        } catch (Exception e) {
            AlguiLog.e(TAG, "加载布局失败: " + layoutName + ", 错误: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取布局资源ID（兼容性方法）
     * @param context 上下文
     * @param layoutName 布局名称
     * @return 资源ID
     */
    private static int getLayoutResourceId(Context context, String layoutName) {
        try {
            return context.getResources().getIdentifier(layoutName, "layout", context.getPackageName());
        } catch (Exception e) {
            AlguiLog.e(TAG, "获取布局资源ID失败: " + layoutName + ", 错误: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 安全地设置Activity的布局
     * @param context Activity上下文
     * @param layoutName 布局名称
     * @return 是否设置成功
     */
    public static boolean setActivityLayout(Context context, String layoutName) {
        if (context == null || layoutName == null || layoutName.isEmpty()) {
            return false;
        }

        try {
            // 首先尝试使用传统方式
            int layoutId = getLayoutResourceId(context, layoutName);
            if (layoutId != 0) {
                // 使用反射调用setContentView
                try {
                    java.lang.reflect.Method setContentViewMethod = context.getClass().getMethod("setContentView", int.class);
                    setContentViewMethod.invoke(context, layoutId);
                    AlguiLog.d(TAG, "使用传统方式设置布局: " + layoutName);
                    return true;
                } catch (Exception e) {
                    AlguiLog.w(TAG, "传统方式设置布局失败: " + layoutName + ", 尝试assets方式");
                }
            }

            // 如果传统方式失败，尝试从assets加载
            View layoutView = loadLayoutFromAssets(context, layoutName);
            if (layoutView != null) {
                try {
                    java.lang.reflect.Method setContentViewMethod = context.getClass().getMethod("setContentView", View.class);
                    setContentViewMethod.invoke(context, layoutView);
                    AlguiLog.d(TAG, "使用assets方式设置布局: " + layoutName);
                    return true;
                } catch (Exception e) {
                    AlguiLog.e(TAG, "assets方式设置布局失败: " + layoutName + ", 错误: " + e.getMessage());
                }
            }

            return false;
        } catch (Exception e) {
            AlguiLog.e(TAG, "设置Activity布局失败: " + layoutName + ", 错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查布局文件是否存在
     * @param context 上下文
     * @param layoutName 布局名称
     * @return 是否存在
     */
    public static boolean isLayoutExists(Context context, String layoutName) {
        if (context == null || layoutName == null || layoutName.isEmpty()) {
            return false;
        }

        // 检查传统资源
        int layoutId = getLayoutResourceId(context, layoutName);
        if (layoutId != 0) {
            return true;
        }

        // 检查assets资源
        String fileName = AlguiToolResource.getLayoutFileName(layoutName);
        return AlguiToolResource.isAssetExists(context, fileName);
    }
} 