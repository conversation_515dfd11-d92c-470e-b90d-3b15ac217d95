package com.bytecat.algui.AlguiTools;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.Log;
import com.bytecat.algui.AlguiManager.AlguiLog;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/24 17:07
 * @Describe 资源访问工具类 - 从assets目录安全加载资源
 */
public class AlguiToolResource {

    public static final String TAG = "AlguiToolResource";

    private AlguiToolResource() {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  

    /**
     * 从assets目录加载图片资源
     * @param context 上下文
     * @param fileName 文件名（不包含路径）
     * @return Drawable对象，加载失败时返回null
     */
    public static Drawable loadDrawableFromAssets(Context context, String fileName) {
        if (context == null || fileName == null || fileName.isEmpty()) {
            return null;
        }

        try {
            // 确保文件名不包含路径
            String cleanFileName = getCleanFileName(fileName);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9.0以上使用ImageDecoder
                return android.graphics.ImageDecoder.decodeDrawable(
                    android.graphics.ImageDecoder.createSource(context.getAssets(), cleanFileName)
                );
            } else {
                // Android 9.0以下使用BitmapFactory
                InputStream inputStream = context.getAssets().open(cleanFileName);
                Bitmap bitmap = BitmapFactory.decodeStream(inputStream);
                inputStream.close();
                
                if (bitmap != null) {
                    return new android.graphics.drawable.BitmapDrawable(context.getResources(), bitmap);
                }
            }
        } catch (Exception e) {
            AlguiLog.w(TAG, "从assets加载图片失败: " + fileName + ", 错误: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从assets目录加载文本资源
     * @param context 上下文
     * @param fileName 文件名（不包含路径）
     * @return 文本内容，加载失败时返回null
     */
    public static String loadTextFromAssets(Context context, String fileName) {
        if (context == null || fileName == null || fileName.isEmpty()) {
            return null;
        }

        try {
            // 确保文件名不包含路径
            String cleanFileName = getCleanFileName(fileName);
            
            InputStream inputStream = context.getAssets().open(cleanFileName);
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();
            
            return new String(buffer, "UTF-8");
        } catch (Exception e) {
            AlguiLog.w(TAG, "从assets加载文本失败: " + fileName + ", 错误: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从assets目录加载二进制资源
     * @param context 上下文
     * @param fileName 文件名（不包含路径）
     * @return 字节数组，加载失败时返回null
     */
    public static byte[] loadBytesFromAssets(Context context, String fileName) {
        if (context == null || fileName == null || fileName.isEmpty()) {
            return null;
        }

        try {
            // 确保文件名不包含路径
            String cleanFileName = getCleanFileName(fileName);
            
            InputStream inputStream = context.getAssets().open(cleanFileName);
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();
            
            return buffer;
        } catch (Exception e) {
            AlguiLog.w(TAG, "从assets加载二进制数据失败: " + fileName + ", 错误: " + e.getMessage());
        }

        return null;
    }

    /**
     * 检查assets目录中是否存在指定文件
     * @param context 上下文
     * @param fileName 文件名（不包含路径）
     * @return 是否存在
     */
    public static boolean isAssetExists(Context context, String fileName) {
        if (context == null || fileName == null || fileName.isEmpty()) {
            return false;
        }

        try {
            // 确保文件名不包含路径
            String cleanFileName = getCleanFileName(fileName);
            
            InputStream inputStream = context.getAssets().open(cleanFileName);
            inputStream.close();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取assets目录中的文件列表
     * @param context 上下文
     * @return 文件列表
     */
    public static String[] getAssetFileList(Context context) {
        if (context == null) {
            return new String[0];
        }

        try {
            return context.getAssets().list("");
        } catch (Exception e) {
            AlguiLog.e(TAG, "获取assets文件列表失败: " + e.getMessage());
            return new String[0];
        }
    }

    /**
     * 清理文件名，移除路径部分
     * @param fileName 原始文件名
     * @return 清理后的文件名
     */
    private static String getCleanFileName(String fileName) {
        if (fileName == null) {
            return "";
        }
        
        // 移除路径部分，只保留文件名
        int lastSlashIndex = fileName.lastIndexOf('/');
        if (lastSlashIndex != -1) {
            return fileName.substring(lastSlashIndex + 1);
        }
        
        return fileName;
    }

    /**
     * 资源映射表 - 将R.drawable.xxx映射到assets中的文件名
     */
    public static class ResourceMap {
        // 图片资源映射
        public static final String DRAWABLE_XYJH = "xyjh.png";
        public static final String DRAWABLE_TXBM = "txbm.png";
        public static final String DRAWABLE_PJYYWL = "pjyywl.png";
        public static final String DRAWABLE_XZTM = "xztm.png";
        public static final String DRAWABLE_YJWY = "yjwy.png";
        public static final String DRAWABLE_IC_LAUNCHER = "icon.png";
        public static final String DRAWABLE_BEIJING1 = "beijing1.png";
        
        // 布局资源映射
        public static final String LAYOUT_ACTIVITY_MAIN = "activity_main.xml";
        public static final String LAYOUT_ACTIVITY_GAME_DETAIL = "activity_game_detail.xml";
        public static final String LAYOUT_ACTIVITY_FLOAT_SELECTION = "activity_float_selection.xml";
        public static final String LAYOUT_ACTIVITY_GUIDE = "activity_guide.xml";
        public static final String LAYOUT_ACTIVITY_GUIDE_WEB = "activity_guide_web.xml";
        public static final String LAYOUT_ACTIVITY_CARD = "activity_card.xml";
        public static final String LAYOUT_DIALOG_ANNOUNCEMENT = "dialog_announcement.xml";
    }

    /**
     * 根据R.drawable.xxx获取对应的assets文件名
     * @param drawableName R.drawable.xxx的名称
     * @return assets中的文件名
     */
    public static String getDrawableFileName(String drawableName) {
        switch (drawableName) {
            case "xyjh":
                return ResourceMap.DRAWABLE_XYJH;
            case "txbm":
                return ResourceMap.DRAWABLE_TXBM;
            case "pjyywl":
                return ResourceMap.DRAWABLE_PJYYWL;
            case "xztm":
                return ResourceMap.DRAWABLE_XZTM;
            case "yjwy":
                return ResourceMap.DRAWABLE_YJWY;
            case "ic_launcher":
                return ResourceMap.DRAWABLE_IC_LAUNCHER;
            case "beijing1":
                return ResourceMap.DRAWABLE_BEIJING1;
            default:
                return drawableName + ".png"; // 默认添加.png后缀
        }
    }

    /**
     * 根据R.layout.xxx获取对应的assets文件名
     * @param layoutName R.layout.xxx的名称
     * @return assets中的文件名
     */
    public static String getLayoutFileName(String layoutName) {
        switch (layoutName) {
            case "activity_main":
                return ResourceMap.LAYOUT_ACTIVITY_MAIN;
            case "activity_game_detail":
                return ResourceMap.LAYOUT_ACTIVITY_GAME_DETAIL;
            case "activity_float_selection":
                return ResourceMap.LAYOUT_ACTIVITY_FLOAT_SELECTION;
            case "activity_guide":
                return ResourceMap.LAYOUT_ACTIVITY_GUIDE;
            case "activity_guide_web":
                return ResourceMap.LAYOUT_ACTIVITY_GUIDE_WEB;
            case "activity_card":
                return ResourceMap.LAYOUT_ACTIVITY_CARD;
            case "dialog_announcement":
                return ResourceMap.LAYOUT_DIALOG_ANNOUNCEMENT;
            default:
                return layoutName + ".xml"; // 默认添加.xml后缀
        }
    }

    /**
     * 测试资源加载功能
     * @param context 上下文
     */
    public static void testResourceLoading(Context context) {
        if (context == null) {
            return;
        }

        AlguiLog.d(TAG, "开始测试资源加载功能...");

        // 测试图片资源加载
        String[] testImages = {"xyjh", "txbm", "pjyywl", "xztm", "yjwy", "ic_launcher", "beijing1"};
        for (String imageName : testImages) {
            String fileName = getDrawableFileName(imageName);
            Drawable drawable = loadDrawableFromAssets(context, fileName);
            if (drawable != null) {
                AlguiLog.d(TAG, "图片加载成功: " + fileName);
            } else {
                AlguiLog.w(TAG, "图片加载失败: " + fileName);
            }
        }

        // 测试布局资源检查
        String[] testLayouts = {"activity_main", "activity_game_detail", "activity_float_selection"};
        for (String layoutName : testLayouts) {
            String fileName = getLayoutFileName(layoutName);
            boolean exists = isAssetExists(context, fileName);
            if (exists) {
                AlguiLog.d(TAG, "布局文件存在: " + fileName);
            } else {
                AlguiLog.w(TAG, "布局文件不存在: " + fileName);
            }
        }

        // 获取assets文件列表
        String[] assetFiles = getAssetFileList(context);
        AlguiLog.d(TAG, "Assets文件列表 (" + assetFiles.length + " 个文件):");
        for (String file : assetFiles) {
            AlguiLog.d(TAG, "  - " + file);
        }

        AlguiLog.d(TAG, "资源加载测试完成");
    }
} 